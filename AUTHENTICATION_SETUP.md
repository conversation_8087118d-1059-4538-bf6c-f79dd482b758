# Authentication & Authorization Setup Guide

This guide covers the complete setup of Supabase authentication and authorization for the FIU Witness Interview application.

## Overview

The authentication system provides:
- **Supabase Auth** for user authentication
- **Role-based access control** (Officer, Supervisor, Admin)
- **Protected API routes** with middleware
- **Frontend auth context** and components
- **Row Level Security** for data protection

## Quick Setup

### 1. Install Required Dependencies

```bash
npm install @supabase/auth-helpers-nextjs @supabase/supabase-js
# or
yarn add @supabase/auth-helpers-nextjs @supabase/supabase-js
```

### 2. Environment Variables

Add to your `.env.local`:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 3. Supabase Configuration

In your Supabase dashboard:

1. **Enable Email Auth**:
   - Go to Authentication > Settings
   - Enable "Enable email confirmations"
   - Set site URL to your domain

2. **Configure Email Templates** (optional):
   - Customize login, signup, and reset password emails

3. **Set up RLS Policies**:
   - The database initialization script includes RLS policies
   - Policies are based on user roles and ownership

## File Structure

```
├── middleware.ts                    # Route protection middleware
├── contexts/
│   └── AuthContext.tsx             # Auth context provider
├── components/
│   └── auth/
│       ├── LoginForm.tsx           # Login component
│       ├── UserMenu.tsx            # User dropdown menu
│       └── ProtectedRoute.tsx      # Route protection wrapper
├── lib/
│   ├── supabase.ts                 # Supabase client & auth service
│   └── database-transformers.ts    # Database type transformers
└── hooks/
    ├── use-cases.ts                # Cases data hooks
    ├── use-interviews.ts           # Interviews data hooks
    └── use-websocket-recording.ts  # WebSocket recording hooks
```

## Authentication Flow

### 1. User Login Process

```typescript
// Frontend login
const { signIn } = useAuth();
await signIn(email, password);

// API validation
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

### 2. Session Management

- **Client-side**: React context manages auth state
- **Server-side**: Middleware validates sessions
- **API routes**: Protected with `withAuth` wrapper

### 3. Role-based Access

```typescript
// Check permissions in components
const { isAdmin, isSupervisor, hasRole } = useAuth();

// Protect routes
<ProtectedRoute requiredRole="admin">
  <AdminPanel />
</ProtectedRoute>

// Protect API endpoints
export const GET = withAuth(handler, { 
  requiredRole: 'supervisor' 
});
```

## User Roles & Permissions

### Officer
- View/edit own cases and interviews
- Create new cases and interviews
- Access transcription and statement features
- Export documents

### Supervisor
- All officer permissions
- View all cases and interviews in department
- Access reports and analytics
- Manage officer assignments

### Admin
- All permissions
- User management
- System configuration
- Full data access

## Security Features

### 1. Route Protection

```typescript
// middleware.ts protects routes automatically
const protectedRoutes = [
  '/dashboard',
  '/cases',
  '/interviews',
  '/api/*'
];
```

### 2. API Security

```typescript
// All API routes use authentication
export const GET = withAuth(async (request: AuthenticatedRequest) => {
  // request.user contains authenticated user
  // request.userRole contains user role
  const cases = await getCasesForUser(request.userId);
  return createSuccessResponse(cases);
});
```

### 3. Database Security

```sql
-- RLS policies ensure data isolation
CREATE POLICY "Officers can view their assigned cases" ON cases
    FOR SELECT USING (
        assigned_officer_id::text = auth.uid()::text 
        OR auth.jwt() ->> 'role' = 'admin'
    );
```

### 4. Rate Limiting

```typescript
// Built-in rate limiting for sensitive endpoints
const rateLimit = checkRateLimit(`login:${clientIP}`, 5, 15 * 60 * 1000);
```

## Implementation Examples

### 1. Wrap Your App with Auth Provider

```typescript
// app/layout.tsx
import { AuthProvider } from '@/contexts/AuthContext';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
```

### 2. Create Protected Pages

```typescript
// app/dashboard/page.tsx
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

export default function Dashboard() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  );
}
```

### 3. Use Auth in Components

```typescript
// components/Header.tsx
import { useAuth } from '@/contexts/AuthContext';
import { UserMenu } from '@/components/auth/UserMenu';

export function Header() {
  const { user, isAuthenticated } = useAuth();

  return (
    <header>
      {isAuthenticated ? (
        <UserMenu />
      ) : (
        <LoginButton />
      )}
    </header>
  );
}
```

### 4. Direct Supabase Service Integration

**Note: API routes have been removed in favor of direct Supabase service calls with RLS (Row Level Security) for authorization.**

```typescript
// Frontend components use Supabase services directly
import { CaseService } from '@/lib/supabase';

// RLS policies handle authorization automatically
const cases = await CaseService.getCases(filters);
```

## Error Handling

### 1. Authentication Errors

```typescript
try {
  await signIn(email, password);
} catch (error) {
  if (error.message.includes('Invalid credentials')) {
    setError('Invalid email or password');
  } else if (error.message.includes('Email not confirmed')) {
    setError('Please check your email and confirm your account');
  } else {
    setError('An unexpected error occurred');
  }
}
```

### 2. Authorization Errors

```typescript
// API returns 403 for insufficient permissions
if (response.status === 403) {
  router.push('/unauthorized');
}
```

## Testing Authentication

### 1. Create Test Users

```sql
-- Insert test users
INSERT INTO users (email, full_name, badge_number, department, role) VALUES
('<EMAIL>', 'System Admin', 'ADMIN001', 'Administration', 'admin'),
('<EMAIL>', 'John Supervisor', 'SUP001', 'Fire Investigation Unit', 'supervisor'),
('<EMAIL>', 'Jane Officer', 'OFF001', 'Fire Investigation Unit', 'officer');
```

### 2. Test Login Flow

```typescript
// Test different user roles
const testUsers = [
  { email: '<EMAIL>', password: 'admin123', expectedRole: 'admin' },
  { email: '<EMAIL>', password: 'super123', expectedRole: 'supervisor' },
  { email: '<EMAIL>', password: 'officer123', expectedRole: 'officer' }
];
```

## Troubleshooting

### Common Issues

1. **"Invalid JWT" errors**
   - Check environment variables
   - Verify Supabase project settings
   - Ensure RLS policies are correct

2. **Infinite redirect loops**
   - Check middleware configuration
   - Verify protected route patterns
   - Ensure auth state is properly managed

3. **Permission denied errors**
   - Verify user roles in database
   - Check RLS policy conditions
   - Ensure JWT contains correct claims

### Debug Tools

```typescript
// Add to components for debugging
const { user } = useAuth();
console.log('Current user:', user);
console.log('User role:', user?.role);
console.log('Is authenticated:', !!user);
```

## Security Best Practices

1. **Always validate on server-side**
2. **Use HTTPS in production**
3. **Implement proper session management**
4. **Regular security audits**
5. **Monitor authentication logs**
6. **Use strong password policies**
7. **Implement account lockout mechanisms**

## Production Deployment

1. **Set production environment variables**
2. **Configure proper CORS settings**
3. **Set up monitoring and logging**
4. **Implement backup authentication methods**
5. **Regular security updates**

This authentication system provides enterprise-grade security while maintaining ease of use for officers in the field.
