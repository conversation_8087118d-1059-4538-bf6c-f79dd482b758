// Database transformation utilities
// Converts between database snake_case and frontend camelCase

import type {
  DatabaseUser,
  DatabaseCase,
  DatabaseInterview,
  DatabaseTranscription,
  DatabaseStatement,
  DatabaseAudioRecording,
  DatabaseExportLog,
  User,
  Case,
  Interview,
  Witness,
  Transcription,
  TranscriptSpeaker,
  TranscriptSegment,
  TranscriptData,
  TranscriptionDataStructure,
  Statement,
  AudioRecording,
  ExportLog,
} from '@/types/database';

// User transformers
export const transformDatabaseUser = (dbUser: DatabaseUser): User => ({
  id: dbUser.id,
  email: dbUser.email,
  fullName: dbUser.full_name,
  badgeNumber: dbUser.badge_number || undefined,
  department: dbUser.department || undefined,
  role: dbUser.role,
  isActive: dbUser.is_active,
  createdAt: new Date(dbUser.created_at),
  updatedAt: new Date(dbUser.updated_at),
});

export const transformUserToDatabase = (user: Partial<User>): Partial<DatabaseUser> => ({
  id: user.id,
  email: user.email,
  full_name: user.fullName,
  badge_number: user.badgeNumber,
  department: user.department,
  role: user.role,
  is_active: user.isActive,
  created_at: user.createdAt?.toISOString(),
  updated_at: user.updatedAt?.toISOString(),
});

// Case transformers
export const transformDatabaseCase = (dbCase: DatabaseCase): Case => ({
  id: dbCase.id,
  incidentLocation: dbCase.incident_location,
  incidentDate: dbCase.incident_date,
  incidentTime: dbCase.incident_time,
  assignedOfficerId: dbCase.assigned_officer_id,
  status: dbCase.status,
  createdAt: new Date(dbCase.created_at),
  updatedAt: new Date(dbCase.updated_at),
});

export const transformCaseToDatabase = (case_: Partial<Case>): Partial<DatabaseCase> => ({
  id: case_.id,
  incident_location: case_.incidentLocation,
  incident_date: case_.incidentDate,
  incident_time: case_.incidentTime,
  assigned_officer_id: case_.assignedOfficerId,
  status: case_.status,
  created_at: case_.createdAt?.toISOString(),
  updated_at: case_.updatedAt?.toISOString(),
});

// Interview transformers
export const transformDatabaseInterview = (dbInterview: DatabaseInterview): Interview => {
  const witness: Witness = {
    name: dbInterview.witness_name,
    type: dbInterview.witness_type,
    contact: dbInterview.witness_contact || '',
    environment: dbInterview.interview_environment || undefined,
  };

  return {
    id: dbInterview.id,
    caseId: dbInterview.case_id,
    interviewingOfficerId: dbInterview.interviewing_officer_id,
    witness,
    status: dbInterview.status,
    startTime: dbInterview.start_time ? new Date(dbInterview.start_time) : undefined,
    endTime: dbInterview.end_time ? new Date(dbInterview.end_time) : undefined,
    duration: dbInterview.duration_seconds || undefined,
    recordingPath: dbInterview.recording_path || undefined,
    createdAt: new Date(dbInterview.created_at),
    updatedAt: new Date(dbInterview.updated_at),
  };
};

// Transform interview with joined data from related tables
export const transformDatabaseInterviewWithJoins = (dbData: any): Interview => {
  const witness: Witness = {
    name: dbData.witness_name,
    type: dbData.witness_type,
    contact: dbData.witness_contact || '',
    environment: dbData.interview_environment || undefined,
  };

  const interview: Interview = {
    id: dbData.id,
    caseId: dbData.case_id,
    interviewingOfficerId: dbData.interviewing_officer_id,
    witness,
    status: dbData.status,
    startTime: dbData.start_time ? new Date(dbData.start_time) : undefined,
    endTime: dbData.end_time ? new Date(dbData.end_time) : undefined,
    duration: dbData.duration_seconds || undefined,
    recordingPath: dbData.recording_path || undefined,
    createdAt: new Date(dbData.created_at),
    updatedAt: new Date(dbData.updated_at),
  };

  // Add joined officer data if available
  if (dbData.interviewing_officer) {
    interview.interviewingOfficer = transformDatabaseUser(dbData.interviewing_officer);
  }

  // Add additional metadata from joins
  (interview as any).caseInfo = dbData.case ? {
    incidentLocation: dbData.case.incident_location,
    incidentDate: dbData.case.incident_date,
    incidentTime: dbData.case.incident_time,
    status: dbData.case.status,
  } : null;

  (interview as any).hasTranscription = dbData.transcriptions && dbData.transcriptions.length > 0;
  (interview as any).hasStatement = dbData.statements && dbData.statements.length > 0;
  (interview as any).transcriptionStatus = dbData.transcriptions?.[0]?.processing_status;
  (interview as any).confidenceScore = dbData.transcriptions?.[0]?.confidence_score;

  return interview;
};

export const transformInterviewToDatabase = (interview: Partial<Interview>): Partial<DatabaseInterview> => ({
  id: interview.id,
  case_id: interview.caseId,
  interviewing_officer_id: interview.interviewingOfficerId,
  witness_name: interview.witness?.name,
  witness_type: interview.witness?.type,
  witness_contact: interview.witness?.contact,
  interview_environment: interview.witness?.environment,
  status: interview.status,
  start_time: interview.startTime?.toISOString(),
  end_time: interview.endTime?.toISOString(),
  duration_seconds: interview.duration,
  recording_path: interview.recordingPath,
  created_at: interview.createdAt?.toISOString(),
  updated_at: interview.updatedAt?.toISOString(),
});

// Transcription transformers
export const transformDatabaseTranscription = (dbTranscription: DatabaseTranscription): Transcription => ({
  id: dbTranscription.id,
  interviewId: dbTranscription.interview_id,
  transcriptionData: dbTranscription.transcription_data,
  language: dbTranscription.language,
  confidenceScore: dbTranscription.confidence_score || undefined,
  processingStatus: dbTranscription.processing_status,
  createdAt: new Date(dbTranscription.created_at),
  updatedAt: new Date(dbTranscription.updated_at),
});

export const transformTranscriptionToDatabase = (
  transcription: Partial<Transcription>
): Partial<DatabaseTranscription> => ({
  id: transcription.id,
  interview_id: transcription.interviewId,
  transcription_data: transcription.transcriptionData,
  language: transcription.language,
  confidence_score: transcription.confidenceScore,
  processing_status: transcription.processingStatus,
  created_at: transcription.createdAt?.toISOString(),
  updated_at: transcription.updatedAt?.toISOString(),
});

// Transform JSONB transcription data to frontend format
export const transformTranscriptionDataToFrontend = (
  transcriptionData: TranscriptionDataStructure
): TranscriptData => ({
  speakers: transcriptionData.speakers.map(speaker => ({
    id: speaker.id,
    name: speaker.name,
    type: speaker.type,
  })),
  segments: transcriptionData.segments.map(segment => ({
    speaker: segment.speaker,
    timestamp: segment.timestamp,
    text: segment.text,
    confidence: segment.confidence,
  })),
  metadata: transcriptionData.metadata ? {
    totalDuration: transcriptionData.metadata.total_duration,
    segmentCount: transcriptionData.metadata.segment_count,
    averageConfidence: transcriptionData.metadata.average_confidence,
  } : undefined,
});

// Transform frontend transcription data to JSONB format
export const transformTranscriptionDataToDatabase = (
  transcriptData: TranscriptData,
  officerName: string,
  witnessName: string
): TranscriptionDataStructure => ({
  speakers: [
    {
      id: 'officer',
      name: officerName,
      type: 'officer',
    },
    {
      id: 'witness',
      name: witnessName,
      type: 'witness',
    },
  ],
  segments: transcriptData.segments.map(segment => ({
    speaker: segment.speaker,
    timestamp: segment.timestamp,
    text: segment.text,
    confidence: segment.confidence,
  })),
  metadata: transcriptData.metadata ? {
    total_duration: transcriptData.metadata.totalDuration,
    segment_count: transcriptData.metadata.segmentCount,
    average_confidence: transcriptData.metadata.averageConfidence,
  } : undefined,
});

// Statement transformers
export const transformDatabaseStatement = (dbStatement: DatabaseStatement): Statement => ({
  id: dbStatement.id,
  interviewId: dbStatement.interview_id,
  content: dbStatement.content,
  officerNotes: dbStatement.officer_notes || undefined,
  version: dbStatement.version,
  createdAt: new Date(dbStatement.created_at),
  updatedAt: new Date(dbStatement.updated_at),
});

export const transformStatementToDatabase = (statement: Partial<Statement>): Partial<DatabaseStatement> => ({
  id: statement.id,
  interview_id: statement.interviewId,
  content: statement.content,
  officer_notes: statement.officerNotes,
  version: statement.version,
  created_at: statement.createdAt?.toISOString(),
  updated_at: statement.updatedAt?.toISOString(),
});

// Audio recording transformers
export const transformDatabaseAudioRecording = (dbRecording: DatabaseAudioRecording): AudioRecording => ({
  id: dbRecording.id,
  interviewId: dbRecording.interview_id,
  filePath: dbRecording.file_path,
  fileSize: dbRecording.file_size || undefined,
  duration: dbRecording.duration_seconds || undefined,
  format: dbRecording.format || undefined,
  sampleRate: dbRecording.sample_rate || undefined,
  channels: dbRecording.channels || undefined,
  createdAt: new Date(dbRecording.created_at),
});

export const transformAudioRecordingToDatabase = (
  recording: Partial<AudioRecording>
): Partial<DatabaseAudioRecording> => ({
  id: recording.id,
  interview_id: recording.interviewId,
  file_path: recording.filePath,
  file_size: recording.fileSize,
  duration_seconds: recording.duration,
  format: recording.format,
  sample_rate: recording.sampleRate,
  channels: recording.channels,
  created_at: recording.createdAt?.toISOString(),
});

// Export log transformers
export const transformDatabaseExportLog = (dbExportLog: DatabaseExportLog): ExportLog => ({
  id: dbExportLog.id,
  interviewId: dbExportLog.interview_id,
  exportType: dbExportLog.export_type,
  filePath: dbExportLog.file_path || undefined,
  exportedByUserId: dbExportLog.exported_by_user_id || undefined,
  createdAt: new Date(dbExportLog.created_at),
});

export const transformExportLogToDatabase = (exportLog: Partial<ExportLog>): Partial<DatabaseExportLog> => ({
  id: exportLog.id,
  interview_id: exportLog.interviewId,
  export_type: exportLog.exportType,
  file_path: exportLog.filePath,
  exported_by_user_id: exportLog.exportedByUserId,
  created_at: exportLog.createdAt?.toISOString(),
});

// Utility functions for batch transformations
export const transformDatabaseUsers = (dbUsers: DatabaseUser[]): User[] =>
  dbUsers.map(transformDatabaseUser);

export const transformDatabaseCases = (dbCases: DatabaseCase[]): Case[] =>
  dbCases.map(transformDatabaseCase);

export const transformDatabaseInterviews = (dbInterviews: DatabaseInterview[]): Interview[] =>
  dbInterviews.map(transformDatabaseInterview);

export const transformDatabaseTranscriptions = (dbTranscriptions: DatabaseTranscription[]): Transcription[] =>
  dbTranscriptions.map(transformDatabaseTranscription);

export const transformDatabaseStatements = (dbStatements: DatabaseStatement[]): Statement[] =>
  dbStatements.map(transformDatabaseStatement);

export const transformDatabaseAudioRecordings = (dbRecordings: DatabaseAudioRecording[]): AudioRecording[] =>
  dbRecordings.map(transformDatabaseAudioRecording);

export const transformDatabaseExportLogs = (dbExportLogs: DatabaseExportLog[]): ExportLog[] =>
  dbExportLogs.map(transformDatabaseExportLog);

// Helper functions for form data transformation
export const createCaseFromFormData = (formData: {
  caseId: string;
  incidentLocation: string;
  incidentDate: string;
  incidentTime: string;
  assignedOfficerId: string;
}): Partial<Case> => ({
  id: formData.caseId,
  incidentLocation: formData.incidentLocation,
  incidentDate: formData.incidentDate,
  incidentTime: formData.incidentTime,
  assignedOfficerId: formData.assignedOfficerId,
  status: 'In Progress',
});

export const createWitnessFromFormData = (formData: {
  witnessName: string;
  witnessType: string;
  witnessContact: string;
  interviewEnvironment?: string;
}): Witness => ({
  name: formData.witnessName,
  type: formData.witnessType as any,
  contact: formData.witnessContact,
  environment: formData.interviewEnvironment as any,
});

// Validation helpers
export const isValidCaseStatus = (status: string): status is Case['status'] =>
  ['In Progress', 'Completed'].includes(status);

export const isValidInterviewStatus = (status: string): status is Interview['status'] =>
  ['scheduled', 'in_progress', 'completed', 'cancelled'].includes(status);

export const isValidWitnessType = (type: string): type is Witness['type'] =>
  ['Resident', 'Neighbor', 'Passerby', 'Business Owner', 'Emergency Responder'].includes(type);

export const isValidInterviewEnvironment = (env: string): env is NonNullable<Witness['environment']> =>
  ['controlled', 'field'].includes(env);

// Date/time formatting utilities
export const formatDate = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

export const formatTime = (time: string): string => {
  const [hours, minutes] = time.split(':');
  const hour = parseInt(hours, 10);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour % 12 || 12;
  return `${displayHour}:${minutes} ${ampm}`;
};

export const formatDateTime = (dateTime: Date | string): string => {
  const dt = typeof dateTime === 'string' ? new Date(dateTime) : dateTime;
  return dt.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};

// Note: API error utilities removed - using direct Supabase error handling instead
