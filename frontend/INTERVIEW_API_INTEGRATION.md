# Interview API Integration

This document explains the integration of the interview API when the "Approve & Export" button is clicked on the transcription screen.

## Overview

When a user clicks the "Approve & Export" button on the transcription screen, the system now automatically creates a complete interview record in the database, including:

1. **Interview Record** - Basic interview information with case and witness details
2. **Transcription Record** - The real-time transcription data from the recording
3. **Statement Record** - The auto-generated summary with detailed officer notes

## Implementation Details

### Files Modified

1. **`frontend/components/transcription-screen.tsx`**
   - Added interview API integration
   - Added loading states and error handling
   - Added comprehensive officer notes generation

2. **`frontend/lib/supabase.ts`**
   - Fixed `InterviewService.createInterview` to include `interviewing_officer_id`

3. **`frontend/app/page.tsx`**
   - Added `currentInterview` to `AppState` interface
   - Updated initial state and reset function

### API Flow

When "Approve & Export" is clicked, the following sequence occurs:

```typescript
1. Validation checks:
   - User is authenticated
   - Case information exists
   - Witness information exists
   - Transcription and summary data exists

2. Create Interview:
   - Creates interview record with case and witness details
   - Sets interviewing officer to current logged-in user

3. Set Interview Timestamps:
   - Starts the interview (sets start_time)
   - Immediately ends the interview (sets end_time and duration)

4. Create Transcription:
   - Saves the real-time transcription data
   - Links to the created interview
   - Includes speaker information and confidence scores

5. Create Statement:
   - Saves the auto-generated summary as statement content
   - Includes comprehensive officer notes with all relevant details

6. Success Handling:
   - Shows success toast with interview ID
   - Updates app state with created interview
   - Navigates to export screen
```

### Data Structure

#### Interview Data
```typescript
{
  interviewing_officer_id: string,  // Current logged-in user ID
  witness: {
    name: string,                   // From app state
    type: WitnessType,             // From app state
    contact: string,               // From app state
    environment?: InterviewEnvironment // From app state
  }
}
```

#### Officer Notes Generated
The system automatically generates comprehensive officer notes including:
- Interview date and time
- Officer information (name, badge, department)
- Case details (ID, location, incident date/time)
- Witness information (name, type, contact, environment)
- Interview metadata (duration, transcript segments)

### Error Handling

The integration includes comprehensive error handling for:
- Authentication errors
- Missing required data
- API failures during interview creation
- Database transaction failures

All errors are displayed to the user via toast notifications with descriptive messages.

### Loading States

The "Approve & Export" button shows:
- Loading spinner during processing
- Disabled state to prevent double-clicks
- "Processing..." text to indicate progress

## Usage

1. Complete a recording session with transcription and summary data
2. Navigate to the transcription screen
3. Review the transcript and summary
4. Click "Approve & Export"
5. System automatically creates all database records
6. Success message displays with interview ID
7. User is navigated to export screen

## Database Tables Affected

- `interviews` - Main interview record
- `transcriptions` - Transcription data and metadata
- `statements` - Statement content and officer notes

## Future Enhancements

Potential improvements for this integration:

1. **Audio File Storage** - Save actual audio recordings to file storage
2. **Export Generation** - Automatically generate PDF/DOCX exports
3. **Email Notifications** - Send confirmation emails to relevant parties
4. **Audit Trail** - Track all changes and access to interview records
5. **Batch Operations** - Support for bulk interview processing
