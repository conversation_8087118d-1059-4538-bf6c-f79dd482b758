# Interview API Integration & Case Detail Enhancement

This document explains the interview API integration and case detail page enhancements.

## Overview

### Interview Creation (Approve & Export)
When a user clicks the "Approve & Export" button on the transcription screen, the system automatically creates a complete interview record in the database, including:

1. **Interview Record** - Basic interview information with case and witness details
2. **Transcription Record** - The real-time transcription data from the recording
3. **Statement Record** - The auto-generated summary with detailed officer notes

### Case Detail Page Enhancement
The case detail page now displays comprehensive interview information using SQL joins to fetch related data from multiple tables.

## Implementation Details

### Files Modified

#### Interview Creation Enhancement:
1. **`frontend/components/transcription-screen.tsx`**
   - Fixed duration calculation to use actual recording start/end times
   - Removed immediate interview status changes
   - Added comprehensive officer notes generation

2. **`frontend/components/recording-screen.tsx`**
   - Added recording end time capture when stopping recording

3. **`frontend/app/page.tsx`**
   - Added `recordingEndTime` to `AppState` interface
   - Added `currentInterview` to track created interviews

4. **`frontend/lib/supabase.ts`**
   - Enhanced `InterviewService.updateInterview` to handle timestamps and duration
   - Added SQL joins to `getInterviews` for comprehensive data fetching

5. **`frontend/types/database.ts`**
   - Extended `UpdateInterviewRequest` interface with timing fields

#### Case Detail Page Enhancement:
6. **`frontend/components/case-detail-screen.tsx`**
   - Complete redesign with comprehensive interview cards
   - Added case information header
   - Added status indicators for transcription and statement completion

7. **`frontend/lib/database-transformers.ts`**
   - Added `transformDatabaseInterviewWithJoins` for handling joined data
   - Enhanced interview transformation with related table data

### API Flow

#### Interview Creation Flow
When "Approve & Export" is clicked, the following sequence occurs:

```typescript
1. Validation checks:
   - User is authenticated
   - Case information exists
   - Witness information exists
   - Transcription and summary data exists

2. Calculate Duration:
   - Uses actual recording start time from app state
   - Uses recording end time (set when recording stopped)
   - Calculates duration in seconds

3. Create Interview:
   - Creates interview record with case and witness details
   - Sets interviewing officer to current logged-in user

4. Update Interview with Timestamps:
   - Sets actual start_time from recording start
   - Sets actual end_time from recording end
   - Sets calculated duration_seconds
   - No status changes (as requested)

5. Create Transcription:
   - Saves the real-time transcription data
   - Links to the created interview
   - Includes speaker information and confidence scores

6. Create Statement:
   - Saves the auto-generated summary as statement content
   - Includes comprehensive officer notes with all relevant details

7. Success Handling:
   - Shows success toast with interview ID
   - Updates app state with created interview
   - Navigates to export screen
```

#### Case Detail Data Fetching
The case detail page uses enhanced SQL queries:

```sql
SELECT interviews.*,
       users.full_name, users.badge_number, users.department,
       cases.incident_location, cases.incident_date, cases.incident_time,
       transcriptions.processing_status, transcriptions.confidence_score,
       statements.id as statement_id
FROM interviews
LEFT JOIN users ON interviews.interviewing_officer_id = users.id
LEFT JOIN cases ON interviews.case_id = cases.id
LEFT JOIN transcriptions ON interviews.id = transcriptions.interview_id
LEFT JOIN statements ON interviews.id = statements.interview_id
WHERE interviews.case_id = ?
ORDER BY interviews.created_at DESC
```

### Data Structure

#### Interview Data
```typescript
{
  interviewing_officer_id: string,  // Current logged-in user ID
  witness: {
    name: string,                   // From app state
    type: WitnessType,             // From app state
    contact: string,               // From app state
    environment?: InterviewEnvironment // From app state
  }
}
```

#### Officer Notes Generated
The system automatically generates comprehensive officer notes including:
- Interview date and time
- Officer information (name, badge, department)
- Case details (ID, location, incident date/time)
- Witness information (name, type, contact, environment)
- Interview metadata (duration, transcript segments)

### Error Handling

The integration includes comprehensive error handling for:
- Authentication errors
- Missing required data
- API failures during interview creation
- Database transaction failures

All errors are displayed to the user via toast notifications with descriptive messages.

### Loading States

The "Approve & Export" button shows:
- Loading spinner during processing
- Disabled state to prevent double-clicks
- "Processing..." text to indicate progress

## Usage

1. Complete a recording session with transcription and summary data
2. Navigate to the transcription screen
3. Review the transcript and summary
4. Click "Approve & Export"
5. System automatically creates all database records
6. Success message displays with interview ID
7. User is navigated to export screen

## Database Tables Affected

- `interviews` - Main interview record
- `transcriptions` - Transcription data and metadata
- `statements` - Statement content and officer notes

## Future Enhancements

Potential improvements for this integration:

1. **Audio File Storage** - Save actual audio recordings to file storage
2. **Export Generation** - Automatically generate PDF/DOCX exports
3. **Email Notifications** - Send confirmation emails to relevant parties
4. **Audit Trail** - Track all changes and access to interview records
5. **Batch Operations** - Support for bulk interview processing
