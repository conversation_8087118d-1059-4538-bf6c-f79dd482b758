"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft } from "lucide-react"
import type { Screen, AppState } from "@/app/page"

interface TranscriptionScreenProps {
  onNavigate: (screen: Screen) => void
  appState: AppState
  updateAppState: (updates: Partial<AppState>) => void
}

export function TranscriptionScreen({ onNavigate, appState, updateAppState }: TranscriptionScreenProps) {
  const currentDate =
    new Date().toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }) +
    " at " +
    new Date().toLocaleTimeString("en-US")

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <h2 className="text-2xl font-semibold">Statement Review</h2>
        <Button variant="outline" size="sm" onClick={() => onNavigate("recording-screen")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Recording
        </Button>
      </div>

      <Card className="mb-8">
        <CardHeader className="bg-secondary">
          <CardTitle className="text-lg">Interview Transcript</CardTitle>
          <p className="text-sm text-muted-foreground">Recorded: {currentDate}</p>
        </CardHeader>
        <CardContent className="p-0">
          <div className="max-h-96 overflow-y-auto p-4">
            {appState.transcriptData?.segments.map((segment, index) => {
              const speaker = appState.transcriptData?.speakers.find((s) => s.id === segment.speaker)
              return (
                <div key={index} className="flex gap-4 mb-4">
                  <div
                    className="min-w-[120px] text-right pr-3 border-r-2 text-sm font-medium"
                    style={{ borderRightColor: speaker?.color }}
                  >
                    {speaker?.name}
                    <div className="text-xs text-muted-foreground font-normal">{segment.timestamp}</div>
                  </div>
                  <div className="flex-1 text-sm leading-relaxed">{segment.text}</div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      <Card className="mb-8">
        <CardHeader className="bg-secondary">
          <CardTitle className="text-lg">Auto-Generated Summary</CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <p className="leading-relaxed">{appState.summaryData}</p>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button variant="secondary" className="flex-1" onClick={() => onNavigate("statement-edit")}>
          Edit Statement
        </Button>
        <Button className="flex-1" onClick={() => onNavigate("export-screen")}>
          Approve & Export
        </Button>
      </div>
    </div>
  )
}
