"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { FileText, Download, Loader2, CheckCircle, AlertCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { generateInterviewPDF, downloadPDF } from "@/lib/pdf-export"
import type { Screen, AppState } from "@/app/page"

interface ExportScreenProps {
  onNavigate: (screen: Screen) => void
  appState: AppState
  resetAppState: () => void
}

export function ExportScreen({ onNavigate, appState, resetAppState }: ExportScreenProps) {
  const { toast } = useToast()
  const [isExporting, setIsExporting] = useState(false)
  const [exportComplete, setExportComplete] = useState(false)

  const exportData = appState.exportData

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60

    if (hours > 0) {
      return `${hours}h ${minutes}m ${remainingSeconds}s`
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`
    } else {
      return `${remainingSeconds}s`
    }
  }

  const handleExportPDF = async () => {
    if (!exportData) {
      toast({
        title: "Export Error",
        description: "No interview data available for export. Please complete an interview first.",
        variant: "destructive",
      })
      return
    }

    setIsExporting(true)
    try {
      // Generate PDF
      const pdfBlob = await generateInterviewPDF(
        exportData.interview!,
        exportData.case!,
        exportData.officer!,
        exportData.transcriptData || undefined,
        exportData.statement || undefined
      )

      // Create filename
      const filename = `Interview_${exportData.interview!.id.slice(-8)}_${exportData.interview!.witness.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`

      // Download and open PDF
      downloadPDF(pdfBlob, filename)

      setExportComplete(true)
      toast({
        title: "Export Successful",
        description: "Interview PDF has been generated and downloaded. A new tab has been opened to view the file.",
      })

    } catch (error) {
      console.error('Error exporting PDF:', error)
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export interview PDF",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  const handleExportWord = () => {
    toast({
      title: "Feature Coming Soon",
      description: "Word document export will be available in a future update.",
    })
  }

  if (!exportData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-semibold">Export Interview</h2>
        </div>

        <Alert variant="destructive" className="max-w-md mx-auto mb-8">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            No interview data available for export. Please complete an interview first.
          </AlertDescription>
        </Alert>

        <div className="flex gap-3 max-w-sm mx-auto">
          <Button
            variant="outline"
            className="flex-1"
            onClick={() => onNavigate("start-screen")}
          >
            Start New Interview
          </Button>
          <Button variant="secondary" className="flex-1" onClick={() => onNavigate("case-history")}>
            View Case History
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-semibold">Export Interview</h2>
      </div>

      {/* Export Status */}
      {exportComplete && (
        <Alert className="max-w-md mx-auto mb-6 border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            Interview has been successfully exported as PDF!
          </AlertDescription>
        </Alert>
      )}

      {/* Interview Summary */}
      <Card className="max-w-2xl mx-auto mb-8">
        <CardHeader>
          <CardTitle>Interview Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold text-sm text-gray-700">Case Information</h4>
              <div className="text-sm space-y-1">
                <p><strong>Case ID:</strong> {exportData.case?.id}</p>
                <p><strong>Location:</strong> {exportData.case?.incidentLocation}</p>
                <p><strong>Date:</strong> {exportData.case?.incidentDate} at {exportData.case?.incidentTime}</p>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-semibold text-sm text-gray-700">Interview Details</h4>
              <div className="text-sm space-y-1">
                <p><strong>Interview ID:</strong> {exportData.interview?.id.slice(-8)}</p>
                <p><strong>Officer:</strong> {exportData.officer?.fullName}</p>
                <p><strong>Witness:</strong> {exportData.interview?.witness.name}</p>
                {exportData.interview?.duration && (
                  <p><strong>Duration:</strong> {formatDuration(exportData.interview.duration)}</p>
                )}
              </div>
            </div>
          </div>

          <div className="mt-4 pt-4 border-t">
            <h4 className="font-semibold text-sm text-gray-700 mb-2">Export Contents</h4>
            <div className="flex flex-wrap gap-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                <CheckCircle className="w-3 h-3 mr-1" />
                Interview Details
              </span>
              {exportData.transcriptData && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Transcription
                </span>
              )}
              {exportData.statement && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Statement
                </span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Export Options */}
      <div className="max-w-sm mx-auto mb-8">
        <h4 className="font-semibold text-center mb-4">Export Format</h4>
        <div className="space-y-3">
          <Button
            className="w-full flex items-center gap-3"
            size="lg"
            onClick={handleExportPDF}
            disabled={isExporting}
          >
            {isExporting ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin" />
                Generating PDF...
              </>
            ) : (
              <>
                <FileText className="w-5 h-5" />
                Export as PDF
              </>
            )}
          </Button>
          <Button
            variant="secondary"
            className="w-full flex items-center gap-3"
            size="lg"
            onClick={handleExportWord}
            disabled={isExporting}
          >
            <Download className="w-5 h-5" />
            Export as Word (Coming Soon)
          </Button>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex gap-3 max-w-sm mx-auto">
        <Button
          variant="outline"
          className="flex-1"
          onClick={() => {
            resetAppState()
            onNavigate("start-screen")
          }}
          disabled={isExporting}
        >
          Start New Interview
        </Button>
        <Button
          variant="secondary"
          className="flex-1"
          onClick={() => onNavigate("case-history")}
          disabled={isExporting}
        >
          View Case History
        </Button>
      </div>
    </div>
  )
}
