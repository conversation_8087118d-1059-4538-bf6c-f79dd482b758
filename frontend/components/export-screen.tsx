"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { FileText, Download } from "lucide-react"
import type { Screen, AppState } from "@/app/page"

interface ExportScreenProps {
  onNavigate: (screen: Screen) => void
  appState: AppState
  resetAppState: () => void
}

export function ExportScreen({ onNavigate, appState, resetAppState }: ExportScreenProps) {
  const currentDate = new Date().toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  })

  const handleExportPDF = () => {
    alert(
      `Statement exported as PDF successfully!\n\nFile: ${appState.currentCase?.id}_${appState.currentWitness?.name.replace(" ", "_")}_Statement.pdf`,
    )
    resetAppState()
    onNavigate("start-screen")
  }

  const handleExportWord = () => {
    alert(
      `Statement exported as Word document successfully!\n\nFile: ${appState.currentCase?.id}_${appState.currentWitness?.name.replace(" ", "_")}_Statement.docx`,
    )
    resetAppState()
    onNavigate("start-screen")
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-semibold">Export Statement</h2>
      </div>

      <Card className="max-w-md mx-auto mb-8">
        <CardContent className="p-6">
          <h4 className="font-semibold mb-4">Statement Ready for Export</h4>
          <div className="space-y-2 text-sm">
            <p>
              <strong>Case:</strong> {appState.currentCase?.id}
            </p>
            <p>
              <strong>Witness:</strong> {appState.currentWitness?.name}
            </p>
            <p>
              <strong>Date:</strong> {currentDate}
            </p>
            <p>
              <strong>Officer:</strong> Detective Johnson
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="max-w-sm mx-auto mb-8">
        <h4 className="font-semibold text-center mb-4">Export Format</h4>
        <div className="space-y-3">
          <Button className="w-full flex items-center gap-3" size="lg" onClick={handleExportPDF}>
            <FileText className="w-5 h-5" />
            Export as PDF
          </Button>
          <Button variant="secondary" className="w-full flex items-center gap-3" size="lg" onClick={handleExportWord}>
            <Download className="w-5 h-5" />
            Export as Word
          </Button>
        </div>
      </div>

      <div className="flex gap-3 max-w-sm mx-auto">
        <Button
          variant="outline"
          className="flex-1"
          onClick={() => {
            resetAppState()
            onNavigate("start-screen")
          }}
        >
          Start New Interview
        </Button>
        <Button variant="secondary" className="flex-1" onClick={() => onNavigate("case-history")}>
          View Case History
        </Button>
      </div>
    </div>
  )
}
