import { Loader2 } from "lucide-react"
import type { Screen, AppState } from "@/app/page"

interface LoadingScreenProps {
  onNavigate: (screen: Screen) => void
  appState: AppState
  updateAppState: (updates: Partial<AppState>) => void
}

export function LoadingScreen({ onNavigate, appState, updateAppState }: LoadingScreenProps) {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col items-center justify-center min-h-[70vh] text-center">
        <Loader2 className="w-16 h-16 animate-spin text-primary mb-6" />
        <h3 className="text-xl font-semibold text-primary mb-3">Processing Statement...</h3>
        <p className="text-muted-foreground max-w-sm">Please wait while we transcribe and generate your statement.</p>
      </div>
    </div>
  )
}
