"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { ArrowLeft } from "lucide-react"
import type { Screen, AppState } from "@/app/page"

interface StatementEditScreenProps {
  onNavigate: (screen: Screen) => void
  appState: AppState
  updateAppState: (updates: Partial<AppState>) => void
}

export function StatementEditScreen({ onNavigate, appState, updateAppState }: StatementEditScreenProps) {
  const [formData, setFormData] = useState({
    caseInfo: "",
    witnessInfo: "",
    statement: "",
    officerNotes: "",
  })

  useEffect(() => {
    if (appState.currentCase && appState.currentWitness) {
      setFormData({
        caseInfo: `${appState.currentCase.id} - ${appState.currentCase.incidentLocation}`,
        witnessInfo: `${appState.currentWitness.name} (${appState.currentWitness.type})`,
        statement: appState.summaryData || "",
        officerNotes: "",
      })
    }
  }, [appState])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    updateAppState({ summaryData: formData.statement })
    onNavigate("export-screen")
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <Button variant="outline" size="sm" onClick={() => onNavigate("transcription-screen")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h2 className="text-2xl font-semibold">Edit Statement</h2>
        <div></div>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="case-info">Case Information</Label>
              <Input id="case-info" value={formData.caseInfo} readOnly className="bg-muted" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="witness-info">Witness Information</Label>
              <Input id="witness-info" value={formData.witnessInfo} readOnly className="bg-muted" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="statement">Statement Summary</Label>
              <Textarea
                id="statement"
                value={formData.statement}
                onChange={(e) => handleInputChange("statement", e.target.value)}
                rows={10}
                className="min-h-[200px]"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="officer-notes">Officer Notes</Label>
              <Textarea
                id="officer-notes"
                value={formData.officerNotes}
                onChange={(e) => handleInputChange("officerNotes", e.target.value)}
                rows={4}
                placeholder="Add any additional notes or observations..."
              />
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="secondary"
                className="flex-1"
                onClick={() => onNavigate("transcription-screen")}
              >
                Cancel
              </Button>
              <Button type="submit" className="flex-1">
                Save Changes
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
