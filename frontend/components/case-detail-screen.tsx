"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Loader2, User, Clock, FileText, Mic, CheckCircle, XCircle } from "lucide-react"
import { useInterviews } from "@/hooks/use-interviews"
import type { Screen, AppState } from "@/app/page"

interface CaseDetailScreenProps {
  onNavigate: (screen: Screen) => void
  appState: AppState
updateAppState: (updates: Partial<AppState>) => void
}


export function CaseDetailsScreen({ onNavigate, appState, updateAppState }: CaseDetailScreenProps) {
  const { interviews, loading, error, refetch } = useInterviews({ caseId: appState.selectedCase?.id });

  console.log('===========', interviews)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(":")
    const hour = Number.parseInt(hours)
    const ampm = hour >= 12 ? "PM" : "AM"
    const displayHour = hour % 12 || 12
    return `${displayHour}:${minutes} ${ampm}`
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const getStatusBadge = (interview: any) => {
    if (interview.hasTranscription && interview.hasStatement) {
      return <Badge variant="default" className="bg-green-100 text-green-800">Complete</Badge>
    } else if (interview.hasTranscription) {
      return <Badge variant="secondary">Transcribed</Badge>
    } else {
      return <Badge variant="outline">Recorded</Badge>
    }
  }
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <Button variant="outline" size="sm" onClick={() => onNavigate("case-history")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h2 className="text-2xl font-semibold">Case Details</h2>
        <div></div>
      </div>

      {/* Case Information Header */}
      {appState.selectedCase && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Case #{appState.selectedCase.id.slice(-8)}</span>
              <Badge variant={appState.selectedCase.status === "In Progress" ? "secondary" : "default"}>
                {appState.selectedCase.status}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Incident Location</p>
                <p className="font-medium">{appState.selectedCase.incidentLocation}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Incident Date & Time</p>
                <p className="font-medium">
                  {formatDate(appState.selectedCase.incidentDate)} at {formatTime(appState.selectedCase.incidentTime)}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Assigned Officer</p>
                <p className="font-medium">{appState.selectedCase.assignedOfficer?.fullName || 'Unknown'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Interviews</p>
                <p className="font-medium">{interviews.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <h3 className="text-xl font-semibold mb-4">Interviews</h3>

      <div className="space-y-4">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin mr-2" />
            <span>Loading case detail...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">Error loading case: {error}</p>
            <Button variant="outline" onClick={refetch}>
              Try Again
            </Button>
          </div>
        ) : interviews.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-600 mb-4">No interviews found for this case</p>
            <Button variant="outline" onClick={refetch}>
              Refresh
            </Button>
          </div>
        ) : (
          interviews.map((interview: any) => (
            <Card key={interview.id} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">Interview #{interview.id.slice(-8)}</CardTitle>
                  {getStatusBadge(interview)}
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Witness Information */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                      <User className="w-4 h-4" />
                      Witness Information
                    </div>
                    <div className="pl-6 space-y-1 text-sm">
                      <p><strong>Name:</strong> {interview.witness.name}</p>
                      <p><strong>Type:</strong> {interview.witness.type}</p>
                      <p><strong>Contact:</strong> {interview.witness.contact}</p>
                      {interview.witness.environment && (
                        <p><strong>Environment:</strong> {interview.witness.environment}</p>
                      )}
                    </div>
                  </div>

                  {/* Interview Details */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                      <Clock className="w-4 h-4" />
                      Interview Details
                    </div>
                    <div className="pl-6 space-y-1 text-sm">
                      <p><strong>Officer:</strong> {interview.interviewingOfficer?.fullName || 'Unknown'}</p>
                      {interview.interviewingOfficer?.badgeNumber && (
                        <p><strong>Badge:</strong> {interview.interviewingOfficer.badgeNumber}</p>
                      )}
                      <p><strong>Created:</strong> {formatDate(interview.createdAt.toISOString().split('T')[0])}</p>
                      {interview.duration && (
                        <p><strong>Duration:</strong> {formatDuration(interview.duration)}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Status Indicators */}
                <div className="flex items-center gap-4 mt-4 pt-4 border-t">
                  <div className="flex items-center gap-2 text-sm">
                    <Mic className="w-4 h-4" />
                    <span>Recording</span>
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  </div>

                  <div className="flex items-center gap-2 text-sm">
                    <FileText className="w-4 h-4" />
                    <span>Transcription</span>
                    {interview.hasTranscription ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <XCircle className="w-4 h-4 text-gray-400" />
                    )}
                  </div>

                  <div className="flex items-center gap-2 text-sm">
                    <FileText className="w-4 h-4" />
                    <span>Statement</span>
                    {interview.hasStatement ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <XCircle className="w-4 h-4 text-gray-400" />
                    )}
                  </div>

                  {interview.confidenceScore && (
                    <div className="flex items-center gap-2 text-sm ml-auto">
                      <span>Confidence: {Math.round(interview.confidenceScore * 100)}%</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
