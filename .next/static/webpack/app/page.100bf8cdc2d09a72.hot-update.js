"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./hooks/use-websocket-recording.ts":
/*!******************************************!*\
  !*** ./hooks/use-websocket-recording.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWebSocketRecording: () => (/* binding */ useWebSocketRecording)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useWebSocketRecording auto */ \nfunction useWebSocketRecording(config) {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isPending, startTransition] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useTransition)();\n    const [transcriptData, setTranscriptData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isPaused, setIsPaused] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const wsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const streamRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const processorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { websocketUrl, sampleRate = 16000, bufferSize = 4096, onTranscriptionUpdate, onError, onConnectionChange } = config;\n    // Initialize default speakers\n    const defaultSpeakers = [\n        {\n            id: \"speaker_1\",\n            name: \"Officer\",\n            type: \"officer\",\n            color: \"#2563eb\"\n        },\n        {\n            id: \"speaker_2\",\n            name: \"Witness\",\n            type: \"witness\",\n            color: \"#dc3545\"\n        }\n    ];\n    // Initialize WebSocket connection\n    const connectWebSocket = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWebSocketRecording.useCallback[connectWebSocket]\": ()=>{\n            try {\n                var _wsRef_current;\n                if (((_wsRef_current = wsRef.current) === null || _wsRef_current === void 0 ? void 0 : _wsRef_current.readyState) === WebSocket.OPEN) {\n                    return;\n                }\n                wsRef.current = new WebSocket(websocketUrl);\n                wsRef.current.onopen = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        setError(null);\n                        onConnectionChange === null || onConnectionChange === void 0 ? void 0 : onConnectionChange(true);\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n                wsRef.current.onmessage = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": (event)=>{\n                        try {\n                            const data = JSON.parse(event.data);\n                            // Handle different message types from the server\n                            if (data.text) {\n                                const newSegment = {\n                                    speaker: data.speaker,\n                                    timestamp: data.end_time,\n                                    text: data.text || ''\n                                };\n                                startTransition({\n                                    \"useWebSocketRecording.useCallback[connectWebSocket]\": ()=>{\n                                        setTranscriptData({\n                                            \"useWebSocketRecording.useCallback[connectWebSocket]\": (prev)=>{\n                                                let updated = {\n                                                    speakers: (prev === null || prev === void 0 ? void 0 : prev.speakers) || defaultSpeakers,\n                                                    segments: (prev === null || prev === void 0 ? void 0 : prev.segments) || []\n                                                };\n                                                if (!prev) {\n                                                    updated = {\n                                                        speakers: defaultSpeakers,\n                                                        segments: [\n                                                            newSegment\n                                                        ]\n                                                    };\n                                                } else if (prev && prev.segments.length > 0 && prev.segments[prev.segments.length - 1].speaker === newSegment.speaker) {\n                                                    updated = {\n                                                        ...prev\n                                                    };\n                                                    updated.segments[updated.segments.length - 1].text = updated.segments[updated.segments.length - 1].text + ' ' + newSegment.text;\n                                                    updated.segments[updated.segments.length - 1].timestamp = newSegment.timestamp;\n                                                } else {\n                                                    updated.segments.push(newSegment);\n                                                }\n                                                onTranscriptionUpdate === null || onTranscriptionUpdate === void 0 ? void 0 : onTranscriptionUpdate(updated);\n                                                return updated;\n                                            }\n                                        }[\"useWebSocketRecording.useCallback[connectWebSocket]\"]);\n                                    }\n                                }[\"useWebSocketRecording.useCallback[connectWebSocket]\"]);\n                            }\n                        // else if (data.text === 'partial_transcription') {\n                        //   // Handle partial/interim transcription results\n                        //   setTranscriptData(prev => {\n                        //     if (!prev) return null;\n                        //     const segments = [...prev.segments];\n                        //     const lastSegment = segments[segments.length - 1];\n                        //     if (lastSegment && data.is_partial) {\n                        //       // Update the last segment with partial text\n                        //       segments[segments.length - 1] = {\n                        //         ...lastSegment,\n                        //         text: data.text || lastSegment.text\n                        //       };\n                        //     }\n                        //     const updated = { ...prev, segments };\n                        //     onTranscriptionUpdate?.(updated);\n                        //     return updated;\n                        //   });\n                        // } \n                        // else if (data.type === 'error') {\n                        //   const errorMsg = data.message || 'Transcription error occurred';\n                        //   setError(errorMsg);\n                        //   onError?.(errorMsg);\n                        // }\n                        } catch (err) {\n                            console.error('Error parsing WebSocket message:', err);\n                            setError('Failed to parse transcription data');\n                        }\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n                wsRef.current.onerror = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": (event)=>{\n                        console.error('WebSocket error:', event);\n                        setError('WebSocket connection error');\n                        onError === null || onError === void 0 ? void 0 : onError('WebSocket connection error');\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n                wsRef.current.onclose = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": ()=>{\n                        console.log('WebSocket disconnected');\n                        setIsConnected(false);\n                        onConnectionChange === null || onConnectionChange === void 0 ? void 0 : onConnectionChange(false);\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n            } catch (err) {\n                console.error('Failed to connect WebSocket:', err);\n                setError('Failed to connect to transcription service');\n                onError === null || onError === void 0 ? void 0 : onError('Failed to connect to transcription service');\n            }\n        }\n    }[\"useWebSocketRecording.useCallback[connectWebSocket]\"], [\n        websocketUrl,\n        onTranscriptionUpdate,\n        onError,\n        onConnectionChange\n    ]);\n    // Format timestamp helper\n    const formatTimestamp = (timestamp)=>{\n        const date = new Date(timestamp);\n        const minutes = date.getMinutes().toString().padStart(2, '0');\n        const seconds = date.getSeconds().toString().padStart(2, '0');\n        return \"\".concat(minutes, \":\").concat(seconds);\n    };\n    // Initialize audio recording\n    const initializeAudio = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: sampleRate,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            streamRef.current = stream;\n            return stream;\n        } catch (err) {\n            throw new Error('Failed to access microphone');\n        }\n    };\n    // Send audio data to WebSocket\n    const sendAudioData = (audioData)=>{\n        var _wsRef_current;\n        if (((_wsRef_current = wsRef.current) === null || _wsRef_current === void 0 ? void 0 : _wsRef_current.readyState) === WebSocket.OPEN && !isPaused) {\n            // Convert Float32Array to Int16Array for better compression\n            const int16Data = new Int16Array(audioData.length);\n            for(let i = 0; i < audioData.length; i++){\n                int16Data[i] = Math.max(-32768, Math.min(32767, audioData[i] * 32768));\n            }\n            // Send as binary data\n            wsRef.current.send(int16Data.buffer);\n        }\n    };\n    // Start recording\n    const startRecording = async ()=>{\n        try {\n            setError(null);\n            // Connect WebSocket if not connected\n            if (!isConnected) {\n                connectWebSocket();\n                // Wait a bit for connection\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            }\n            // Initialize audio\n            const stream = await initializeAudio();\n            // Create audio context for processing\n            audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)({\n                sampleRate: sampleRate\n            });\n            const source = audioContextRef.current.createMediaStreamSource(stream);\n            processorRef.current = audioContextRef.current.createScriptProcessor(bufferSize, 1, 1);\n            processorRef.current.onaudioprocess = (event)=>{\n                const inputData = event.inputBuffer.getChannelData(0);\n                sendAudioData(inputData);\n            };\n            source.connect(processorRef.current);\n            processorRef.current.connect(audioContextRef.current.destination);\n            setIsRecording(true);\n            setIsPaused(false);\n            // Initialize transcript data if not exists\n            if (!transcriptData) {\n                setTranscriptData({\n                    speakers: defaultSpeakers,\n                    segments: []\n                });\n            }\n        } catch (err) {\n            const errorMsg = err instanceof Error ? err.message : 'Failed to start recording';\n            setError(errorMsg);\n            onError === null || onError === void 0 ? void 0 : onError(errorMsg);\n            throw err;\n        }\n    };\n    // Stop recording\n    const stopRecording = ()=>{\n        setIsRecording(false);\n        setIsPaused(false);\n        // Stop audio processing\n        if (processorRef.current) {\n            processorRef.current.disconnect();\n            processorRef.current = null;\n        }\n        if (audioContextRef.current) {\n            audioContextRef.current.close();\n            audioContextRef.current = null;\n        }\n        if (streamRef.current) {\n            streamRef.current.getTracks().forEach((track)=>track.stop());\n            streamRef.current = null;\n        }\n        // Close WebSocket\n        if (wsRef.current) {\n            wsRef.current.close();\n            wsRef.current = null;\n        }\n    };\n    // Pause recording\n    const pauseRecording = ()=>{\n        setIsPaused(true);\n    };\n    // Resume recording\n    const resumeRecording = ()=>{\n        setIsPaused(false);\n    };\n    // Clear transcript\n    const clearTranscript = ()=>{\n        setTranscriptData({\n            speakers: defaultSpeakers,\n            segments: []\n        });\n    };\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWebSocketRecording.useEffect\": ()=>{\n            return ({\n                \"useWebSocketRecording.useEffect\": ()=>{\n                    stopRecording();\n                }\n            })[\"useWebSocketRecording.useEffect\"];\n        }\n    }[\"useWebSocketRecording.useEffect\"], []);\n    return {\n        isConnected,\n        isRecording,\n        transcriptData,\n        error,\n        startRecording,\n        stopRecording,\n        pauseRecording,\n        resumeRecording,\n        clearTranscript\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/use-websocket-recording.ts\n"));

/***/ })

});