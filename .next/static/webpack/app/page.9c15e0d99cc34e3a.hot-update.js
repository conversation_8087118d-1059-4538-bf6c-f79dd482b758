"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/recording-screen.tsx":
/*!*****************************************!*\
  !*** ./components/recording-screen.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecordingScreen: () => (/* binding */ RecordingScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Pause,Play,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Pause,Play,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Pause,Play,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Pause,Play,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Pause,Play,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Pause,Play,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _hooks_use_websocket_recording__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-websocket-recording */ \"(app-pages-browser)/./hooks/use-websocket-recording.ts\");\n/* __next_internal_client_entry_do_not_use__ RecordingScreen auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// TODO: Replace with your actual websocket endpoint\nconst WEBSOCKET_ENDPOINT = \"ws://localhost:8080/ws/transcribe\" // Replace this with your actual endpoint\n;\nfunction RecordingScreen(param) {\n    let { onNavigate, appState, updateAppState } = param;\n    var _appState_currentCase, _appState_currentWitness;\n    _s();\n    const [recordingTime, setRecordingTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"00:00\");\n    const [startTime, setStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize websocket recording\n    const { isConnected, isRecording: wsIsRecording, transcriptData, error: wsError, startRecording: wsStartRecording, stopRecording: wsStopRecording, pauseRecording: wsPauseRecording, resumeRecording: wsResumeRecording } = (0,_hooks_use_websocket_recording__WEBPACK_IMPORTED_MODULE_6__.useWebSocketRecording)({\n        websocketUrl: WEBSOCKET_ENDPOINT,\n        onTranscriptionUpdate: {\n            \"RecordingScreen.useWebSocketRecording\": (data)=>{\n                // Update app state with new transcription data\n                updateAppState({\n                    transcriptData: data\n                });\n            }\n        }[\"RecordingScreen.useWebSocketRecording\"],\n        onError: {\n            \"RecordingScreen.useWebSocketRecording\": (error)=>{\n                console.error('WebSocket recording error:', error);\n            }\n        }[\"RecordingScreen.useWebSocketRecording\"],\n        onConnectionChange: {\n            \"RecordingScreen.useWebSocketRecording\": (connected)=>{\n                console.log('WebSocket connection status:', connected);\n            }\n        }[\"RecordingScreen.useWebSocketRecording\"]\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecordingScreen.useEffect\": ()=>{\n            // Auto-start recording when component mounts\n            if (!appState.isRecording && !wsIsRecording) {\n                handleStartRecording();\n            }\n        }\n    }[\"RecordingScreen.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecordingScreen.useEffect\": ()=>{\n            let interval = null;\n            if (appState.isRecording && !appState.isPaused && startTime) {\n                interval = setInterval({\n                    \"RecordingScreen.useEffect\": ()=>{\n                        const elapsed = Date.now() - startTime;\n                        const minutes = Math.floor(elapsed / 60000);\n                        const seconds = Math.floor(elapsed % 60000 / 1000);\n                        setRecordingTime(\"\".concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\")));\n                    }\n                }[\"RecordingScreen.useEffect\"], 1000);\n            }\n            return ({\n                \"RecordingScreen.useEffect\": ()=>{\n                    if (interval) clearInterval(interval);\n                }\n            })[\"RecordingScreen.useEffect\"];\n        }\n    }[\"RecordingScreen.useEffect\"], [\n        appState.isRecording,\n        appState.isPaused,\n        startTime\n    ]);\n    const handleStartRecording = async ()=>{\n        try {\n            const now = Date.now();\n            setStartTime(now);\n            // Start websocket recording\n            await wsStartRecording();\n            updateAppState({\n                isRecording: true,\n                isPaused: false,\n                recordingStartTime: now\n            });\n        } catch (error) {\n            console.error('Failed to start recording:', error);\n        }\n    };\n    const handlePauseRecording = ()=>{\n        wsPauseRecording();\n        updateAppState({\n            isPaused: true\n        });\n    };\n    const handleResumeRecording = ()=>{\n        wsResumeRecording();\n        updateAppState({\n            isPaused: false\n        });\n    };\n    const handleStopRecording = ()=>{\n        // Stop websocket recording\n        wsStopRecording();\n        updateAppState({\n            isRecording: false,\n            isPaused: false\n        });\n        // Navigate directly to transcription screen with real data\n        onNavigate(\"transcription-screen\");\n    };\n    // Use real transcription data or fallback to empty state\n    const currentTranscriptData = transcriptData || appState.transcriptData;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8 flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-8 pb-4 border-b flex-shrink-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold\",\n                        children: \"Recording Interview\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: isConnected ? 'Connected' : 'Disconnected'\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        children: recordingTime\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            wsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                        children: wsError\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-8 flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Case:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    (_appState_currentCase = appState.currentCase) === null || _appState_currentCase === void 0 ? void 0 : _appState_currentCase.id\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Witness:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    (_appState_currentWitness = appState.currentWitness) === null || _appState_currentWitness === void 0 ? void 0 : _appState_currentWitness.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-8 flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto p-4\",\n                        children: (currentTranscriptData === null || currentTranscriptData === void 0 ? void 0 : currentTranscriptData.segments) && currentTranscriptData.segments.length > 0 ? currentTranscriptData.segments.map((segment, index)=>{\n                            const speaker = currentTranscriptData.speakers.find((s)=>s.id === segment.speaker);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"min-w-[120px] text-right pr-3 border-r-2 text-sm font-medium\",\n                                        style: {\n                                            borderRightColor: speaker === null || speaker === void 0 ? void 0 : speaker.color\n                                        },\n                                        children: [\n                                            speaker === null || speaker === void 0 ? void 0 : speaker.name,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-muted-foreground font-normal\",\n                                                children: segment.timestamp\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-sm leading-relaxed\",\n                                        children: segment.text\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 19\n                            }, this);\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-2\",\n                                    children: \"Waiting for transcription...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: isConnected ? 'Connected to transcription service' : 'Connecting to transcription service...'\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col gap-4 max-w-sm mx-auto flex-shrink-0\",\n                children: [\n                    !appState.isPaused ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"secondary\",\n                        size: \"lg\",\n                        onClick: handlePauseRecording,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this),\n                            \"Pause\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"secondary\",\n                        size: \"lg\",\n                        onClick: handleResumeRecording,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this),\n                            \"Resume\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        size: \"lg\",\n                        onClick: handleStopRecording,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            \"Stop Interview\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_s(RecordingScreen, \"L3vOjsdQIozzjp+hJ5s0AYQ6mbI=\", false, function() {\n    return [\n        _hooks_use_websocket_recording__WEBPACK_IMPORTED_MODULE_6__.useWebSocketRecording\n    ];\n});\n_c = RecordingScreen;\nvar _c;\n$RefreshReg$(_c, \"RecordingScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvcmVjb3JkaW5nLXNjcmVlbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMEQ7QUFDWDtBQUNTO0FBQ1g7QUFDa0I7QUFDZTtBQUNQO0FBVXZFLG9EQUFvRDtBQUNwRCxNQUFNZSxxQkFBcUIsb0NBQW9DLHlDQUF5Qzs7QUFFakcsU0FBU0MsZ0JBQWdCLEtBQThEO1FBQTlELEVBQUVDLFVBQVUsRUFBRUMsUUFBUSxFQUFFQyxjQUFjLEVBQXdCLEdBQTlEO1FBdUlNRCx1QkFHR0E7O0lBekl2QyxNQUFNLENBQUNFLGVBQWVDLGlCQUFpQixHQUFHckIsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDc0IsV0FBV0MsYUFBYSxHQUFHdkIsK0NBQVFBLENBQWdCO0lBRTFELGlDQUFpQztJQUNqQyxNQUFNLEVBQ0p3QixXQUFXLEVBQ1hDLGFBQWFDLGFBQWEsRUFDMUJDLGNBQWMsRUFDZEMsT0FBT0MsT0FBTyxFQUNkQyxnQkFBZ0JDLGdCQUFnQixFQUNoQ0MsZUFBZUMsZUFBZSxFQUM5QkMsZ0JBQWdCQyxnQkFBZ0IsRUFDaENDLGlCQUFpQkMsaUJBQWlCLEVBQ25DLEdBQUd2QixxRkFBcUJBLENBQUM7UUFDeEJ3QixjQUFjdkI7UUFDZHdCLHFCQUFxQjtxREFBRSxDQUFDQztnQkFDdEIsK0NBQStDO2dCQUMvQ3JCLGVBQWU7b0JBQUVRLGdCQUFnQmE7Z0JBQUs7WUFDeEM7O1FBQ0FDLE9BQU87cURBQUUsQ0FBQ2I7Z0JBQ1JjLFFBQVFkLEtBQUssQ0FBQyw4QkFBOEJBO1lBQzlDOztRQUNBZSxrQkFBa0I7cURBQUUsQ0FBQ0M7Z0JBQ25CRixRQUFRRyxHQUFHLENBQUMsZ0NBQWdDRDtZQUM5Qzs7SUFDRjtJQUVBM0MsZ0RBQVNBO3FDQUFDO1lBQ1IsNkNBQTZDO1lBQzdDLElBQUksQ0FBQ2lCLFNBQVNPLFdBQVcsSUFBSSxDQUFDQyxlQUFlO2dCQUMzQ29CO1lBQ0Y7UUFDRjtvQ0FBRyxFQUFFO0lBRUw3QyxnREFBU0E7cUNBQUM7WUFDUixJQUFJOEMsV0FBa0M7WUFFdEMsSUFBSTdCLFNBQVNPLFdBQVcsSUFBSSxDQUFDUCxTQUFTOEIsUUFBUSxJQUFJMUIsV0FBVztnQkFDM0R5QixXQUFXRTtpREFBWTt3QkFDckIsTUFBTUMsVUFBVUMsS0FBS0MsR0FBRyxLQUFLOUI7d0JBQzdCLE1BQU0rQixVQUFVQyxLQUFLQyxLQUFLLENBQUNMLFVBQVU7d0JBQ3JDLE1BQU1NLFVBQVVGLEtBQUtDLEtBQUssQ0FBQyxVQUFXLFFBQVM7d0JBQy9DbEMsaUJBQWlCLEdBQTBDbUMsT0FBdkNILFFBQVFJLFFBQVEsR0FBR0MsUUFBUSxDQUFDLEdBQUcsTUFBSyxLQUF1QyxPQUFwQ0YsUUFBUUMsUUFBUSxHQUFHQyxRQUFRLENBQUMsR0FBRztvQkFDNUY7Z0RBQUc7WUFDTDtZQUVBOzZDQUFPO29CQUNMLElBQUlYLFVBQVVZLGNBQWNaO2dCQUM5Qjs7UUFDRjtvQ0FBRztRQUFDN0IsU0FBU08sV0FBVztRQUFFUCxTQUFTOEIsUUFBUTtRQUFFMUI7S0FBVTtJQUV2RCxNQUFNd0IsdUJBQXVCO1FBQzNCLElBQUk7WUFDRixNQUFNTSxNQUFNRCxLQUFLQyxHQUFHO1lBQ3BCN0IsYUFBYTZCO1lBRWIsNEJBQTRCO1lBQzVCLE1BQU1yQjtZQUVOWixlQUFlO2dCQUNiTSxhQUFhO2dCQUNidUIsVUFBVTtnQkFDVlksb0JBQW9CUjtZQUN0QjtRQUNGLEVBQUUsT0FBT3hCLE9BQU87WUFDZGMsUUFBUWQsS0FBSyxDQUFDLDhCQUE4QkE7UUFDOUM7SUFDRjtJQUVBLE1BQU1pQyx1QkFBdUI7UUFDM0IxQjtRQUNBaEIsZUFBZTtZQUFFNkIsVUFBVTtRQUFLO0lBQ2xDO0lBRUEsTUFBTWMsd0JBQXdCO1FBQzVCekI7UUFDQWxCLGVBQWU7WUFBRTZCLFVBQVU7UUFBTTtJQUNuQztJQUVBLE1BQU1lLHNCQUFzQjtRQUMxQiwyQkFBMkI7UUFDM0I5QjtRQUVBZCxlQUFlO1lBQ2JNLGFBQWE7WUFDYnVCLFVBQVU7UUFDWjtRQUVBLDJEQUEyRDtRQUMzRC9CLFdBQVc7SUFDYjtJQUVBLHlEQUF5RDtJQUN6RCxNQUFNK0Msd0JBQXdCckMsa0JBQWtCVCxTQUFTUyxjQUFjO0lBRXZFLHFCQUNFLDhEQUFDc0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUdELFdBQVU7a0NBQXlCOzs7Ozs7a0NBQ3ZDLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBRWIsOERBQUNEO2dDQUFJQyxXQUFVOztvQ0FDWjFDLDRCQUNDLDhEQUFDYixzSEFBSUE7d0NBQUN1RCxXQUFVOzs7Ozs2REFFaEIsOERBQUN0RCxzSEFBT0E7d0NBQUNzRCxXQUFVOzs7Ozs7a0RBRXJCLDhEQUFDRTt3Q0FBS0YsV0FBVTtrREFDYjFDLGNBQWMsY0FBYzs7Ozs7Ozs7Ozs7OzBDQUtqQyw4REFBQ3lDO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7Ozs7OztrREFDZiw4REFBQzdELHVEQUFLQTt3Q0FBQ2dFLFNBQVE7a0RBQWFqRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTWpDUyx5QkFDQyw4REFBQ3ZCLHVEQUFLQTtnQkFBQzRELFdBQVU7O2tDQUNmLDhEQUFDckQsc0hBQVdBO3dCQUFDcUQsV0FBVTs7Ozs7O2tDQUN2Qiw4REFBQzNELGtFQUFnQkE7a0NBQ2RzQjs7Ozs7Ozs7Ozs7OzBCQUtQLDhEQUFDMUIscURBQUlBO2dCQUFDK0QsV0FBVTswQkFDZCw0RUFBQzlELDREQUFXQTtvQkFBQzhELFdBQVU7OEJBQ3JCLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNJOztrREFDQyw4REFBQ0M7a0RBQU87Ozs7OztvQ0FBYztxQ0FBRXJELHdCQUFBQSxTQUFTc0QsV0FBVyxjQUFwQnRELDRDQUFBQSxzQkFBc0J1RCxFQUFFOzs7Ozs7OzBDQUVsRCw4REFBQ0g7O2tEQUNDLDhEQUFDQztrREFBTzs7Ozs7O29DQUFpQjtxQ0FBRXJELDJCQUFBQSxTQUFTd0QsY0FBYyxjQUF2QnhELCtDQUFBQSx5QkFBeUJ5RCxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNaEUsOERBQUN4RSxxREFBSUE7Z0JBQUMrRCxXQUFVOzBCQUNkLDRFQUFDOUQsNERBQVdBO29CQUFDOEQsV0FBVTs4QkFDckIsNEVBQUNEO3dCQUFJQyxXQUFVO2tDQUNaRixDQUFBQSxrQ0FBQUEsNENBQUFBLHNCQUF1QlksUUFBUSxLQUFJWixzQkFBc0JZLFFBQVEsQ0FBQ0MsTUFBTSxHQUFHLElBQzFFYixzQkFBc0JZLFFBQVEsQ0FBQ0UsR0FBRyxDQUFDLENBQUNDLFNBQVNDOzRCQUMzQyxNQUFNQyxVQUFVakIsc0JBQXNCa0IsUUFBUSxDQUFDQyxJQUFJLENBQUMsQ0FBQ0MsSUFBTUEsRUFBRVgsRUFBRSxLQUFLTSxRQUFRRSxPQUFPOzRCQUNuRixxQkFDRSw4REFBQ2hCO2dDQUFnQkMsV0FBVTs7a0RBQ3pCLDhEQUFDRDt3Q0FDQ0MsV0FBVTt3Q0FDVm1CLE9BQU87NENBQUVDLGdCQUFnQixFQUFFTCxvQkFBQUEsOEJBQUFBLFFBQVNNLEtBQUs7d0NBQUM7OzRDQUV6Q04sb0JBQUFBLDhCQUFBQSxRQUFTTixJQUFJOzBEQUNkLDhEQUFDVjtnREFBSUMsV0FBVTswREFBNkNhLFFBQVFTLFNBQVM7Ozs7Ozs7Ozs7OztrREFFL0UsOERBQUN2Qjt3Q0FBSUMsV0FBVTtrREFBa0NhLFFBQVFVLElBQUk7Ozs7Ozs7K0JBUnJEVDs7Ozs7d0JBV2QsbUJBRUEsOERBQUNmOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0k7b0NBQUVKLFdBQVU7OENBQU87Ozs7Ozs4Q0FDcEIsOERBQUNJO29DQUFFSixXQUFVOzhDQUNWMUMsY0FBYyx1Q0FBdUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRbEUsOERBQUN5QztnQkFBSUMsV0FBVTs7b0JBQ1osQ0FBQ2hELFNBQVM4QixRQUFRLGlCQUNqQiw4REFBQzlDLHlEQUFNQTt3QkFBQ21FLFNBQVE7d0JBQVlxQixNQUFLO3dCQUFLQyxTQUFTOUI7d0JBQXNCSyxXQUFVOzswQ0FDN0UsOERBQUMxRCx1SEFBS0E7Z0NBQUMwRCxXQUFVOzs7Ozs7NEJBQVk7Ozs7Ozs2Q0FJL0IsOERBQUNoRSx5REFBTUE7d0JBQUNtRSxTQUFRO3dCQUFZcUIsTUFBSzt3QkFBS0MsU0FBUzdCO3dCQUF1QkksV0FBVTs7MENBQzlFLDhEQUFDekQsdUhBQUlBO2dDQUFDeUQsV0FBVTs7Ozs7OzRCQUFZOzs7Ozs7O2tDQUtoQyw4REFBQ2hFLHlEQUFNQTt3QkFBQ3dGLE1BQUs7d0JBQUtDLFNBQVM1Qjt3QkFBcUJHLFdBQVU7OzBDQUN4RCw4REFBQ3hELHVIQUFNQTtnQ0FBQ3dELFdBQVU7Ozs7Ozs0QkFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU14QztHQW5NZ0JsRDs7UUFjVkYsaUZBQXFCQTs7O0tBZFhFIiwic291cmNlcyI6WyIvaG9tZS9jbGFya25ndXllbi9EZXNrdG9wL2Rldi9zY2RmL2NvbXBvbmVudHMvcmVjb3JkaW5nLXNjcmVlbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlVHJhbnNpdGlvbiB9IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIlxuaW1wb3J0IHsgQWxlcnQsIEFsZXJ0RGVzY3JpcHRpb24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2FsZXJ0XCJcbmltcG9ydCB7IFBhdXNlLCBQbGF5LCBTcXVhcmUsIFdpZmksIFdpZmlPZmYsIEFsZXJ0Q2lyY2xlIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiXG5pbXBvcnQgeyB1c2VXZWJTb2NrZXRSZWNvcmRpbmcgfSBmcm9tIFwiQC9ob29rcy91c2Utd2Vic29ja2V0LXJlY29yZGluZ1wiXG5pbXBvcnQgdHlwZSB7IFNjcmVlbiwgQXBwU3RhdGUgfSBmcm9tIFwiQC9hcHAvcGFnZVwiXG5pbXBvcnQgdHlwZSB7IFRyYW5zY3JpcHREYXRhIH0gZnJvbSBcIkAvdHlwZXMvZGF0YWJhc2VcIlxuXG5pbnRlcmZhY2UgUmVjb3JkaW5nU2NyZWVuUHJvcHMge1xuICBvbk5hdmlnYXRlOiAoc2NyZWVuOiBTY3JlZW4pID0+IHZvaWRcbiAgYXBwU3RhdGU6IEFwcFN0YXRlXG4gIHVwZGF0ZUFwcFN0YXRlOiAodXBkYXRlczogUGFydGlhbDxBcHBTdGF0ZT4pID0+IHZvaWRcbn1cblxuLy8gVE9ETzogUmVwbGFjZSB3aXRoIHlvdXIgYWN0dWFsIHdlYnNvY2tldCBlbmRwb2ludFxuY29uc3QgV0VCU09DS0VUX0VORFBPSU5UID0gXCJ3czovL2xvY2FsaG9zdDo4MDgwL3dzL3RyYW5zY3JpYmVcIiAvLyBSZXBsYWNlIHRoaXMgd2l0aCB5b3VyIGFjdHVhbCBlbmRwb2ludFxuXG5leHBvcnQgZnVuY3Rpb24gUmVjb3JkaW5nU2NyZWVuKHsgb25OYXZpZ2F0ZSwgYXBwU3RhdGUsIHVwZGF0ZUFwcFN0YXRlIH06IFJlY29yZGluZ1NjcmVlblByb3BzKSB7XG4gIGNvbnN0IFtyZWNvcmRpbmdUaW1lLCBzZXRSZWNvcmRpbmdUaW1lXSA9IHVzZVN0YXRlKFwiMDA6MDBcIilcbiAgY29uc3QgW3N0YXJ0VGltZSwgc2V0U3RhcnRUaW1lXSA9IHVzZVN0YXRlPG51bWJlciB8IG51bGw+KG51bGwpXG5cbiAgLy8gSW5pdGlhbGl6ZSB3ZWJzb2NrZXQgcmVjb3JkaW5nXG4gIGNvbnN0IHtcbiAgICBpc0Nvbm5lY3RlZCxcbiAgICBpc1JlY29yZGluZzogd3NJc1JlY29yZGluZyxcbiAgICB0cmFuc2NyaXB0RGF0YSxcbiAgICBlcnJvcjogd3NFcnJvcixcbiAgICBzdGFydFJlY29yZGluZzogd3NTdGFydFJlY29yZGluZyxcbiAgICBzdG9wUmVjb3JkaW5nOiB3c1N0b3BSZWNvcmRpbmcsXG4gICAgcGF1c2VSZWNvcmRpbmc6IHdzUGF1c2VSZWNvcmRpbmcsXG4gICAgcmVzdW1lUmVjb3JkaW5nOiB3c1Jlc3VtZVJlY29yZGluZyxcbiAgfSA9IHVzZVdlYlNvY2tldFJlY29yZGluZyh7XG4gICAgd2Vic29ja2V0VXJsOiBXRUJTT0NLRVRfRU5EUE9JTlQsXG4gICAgb25UcmFuc2NyaXB0aW9uVXBkYXRlOiAoZGF0YSkgPT4ge1xuICAgICAgLy8gVXBkYXRlIGFwcCBzdGF0ZSB3aXRoIG5ldyB0cmFuc2NyaXB0aW9uIGRhdGFcbiAgICAgIHVwZGF0ZUFwcFN0YXRlKHsgdHJhbnNjcmlwdERhdGE6IGRhdGEgfSk7XG4gICAgfSxcbiAgICBvbkVycm9yOiAoZXJyb3IpID0+IHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1dlYlNvY2tldCByZWNvcmRpbmcgZXJyb3I6JywgZXJyb3IpO1xuICAgIH0sXG4gICAgb25Db25uZWN0aW9uQ2hhbmdlOiAoY29ubmVjdGVkKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnV2ViU29ja2V0IGNvbm5lY3Rpb24gc3RhdHVzOicsIGNvbm5lY3RlZCk7XG4gICAgfSxcbiAgfSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBBdXRvLXN0YXJ0IHJlY29yZGluZyB3aGVuIGNvbXBvbmVudCBtb3VudHNcbiAgICBpZiAoIWFwcFN0YXRlLmlzUmVjb3JkaW5nICYmICF3c0lzUmVjb3JkaW5nKSB7XG4gICAgICBoYW5kbGVTdGFydFJlY29yZGluZygpXG4gICAgfVxuICB9LCBbXSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxldCBpbnRlcnZhbDogTm9kZUpTLlRpbWVvdXQgfCBudWxsID0gbnVsbFxuXG4gICAgaWYgKGFwcFN0YXRlLmlzUmVjb3JkaW5nICYmICFhcHBTdGF0ZS5pc1BhdXNlZCAmJiBzdGFydFRpbWUpIHtcbiAgICAgIGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgICBjb25zdCBlbGFwc2VkID0gRGF0ZS5ub3coKSAtIHN0YXJ0VGltZVxuICAgICAgICBjb25zdCBtaW51dGVzID0gTWF0aC5mbG9vcihlbGFwc2VkIC8gNjAwMDApXG4gICAgICAgIGNvbnN0IHNlY29uZHMgPSBNYXRoLmZsb29yKChlbGFwc2VkICUgNjAwMDApIC8gMTAwMClcbiAgICAgICAgc2V0UmVjb3JkaW5nVGltZShgJHttaW51dGVzLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgXCIwXCIpfToke3NlY29uZHMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCBcIjBcIil9YClcbiAgICAgIH0sIDEwMDApXG4gICAgfVxuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlmIChpbnRlcnZhbCkgY2xlYXJJbnRlcnZhbChpbnRlcnZhbClcbiAgICB9XG4gIH0sIFthcHBTdGF0ZS5pc1JlY29yZGluZywgYXBwU3RhdGUuaXNQYXVzZWQsIHN0YXJ0VGltZV0pXG5cbiAgY29uc3QgaGFuZGxlU3RhcnRSZWNvcmRpbmcgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IG5vdyA9IERhdGUubm93KClcbiAgICAgIHNldFN0YXJ0VGltZShub3cpXG5cbiAgICAgIC8vIFN0YXJ0IHdlYnNvY2tldCByZWNvcmRpbmdcbiAgICAgIGF3YWl0IHdzU3RhcnRSZWNvcmRpbmcoKVxuXG4gICAgICB1cGRhdGVBcHBTdGF0ZSh7XG4gICAgICAgIGlzUmVjb3JkaW5nOiB0cnVlLFxuICAgICAgICBpc1BhdXNlZDogZmFsc2UsXG4gICAgICAgIHJlY29yZGluZ1N0YXJ0VGltZTogbm93LFxuICAgICAgfSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHN0YXJ0IHJlY29yZGluZzonLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVQYXVzZVJlY29yZGluZyA9ICgpID0+IHtcbiAgICB3c1BhdXNlUmVjb3JkaW5nKClcbiAgICB1cGRhdGVBcHBTdGF0ZSh7IGlzUGF1c2VkOiB0cnVlIH0pXG4gIH1cblxuICBjb25zdCBoYW5kbGVSZXN1bWVSZWNvcmRpbmcgPSAoKSA9PiB7XG4gICAgd3NSZXN1bWVSZWNvcmRpbmcoKVxuICAgIHVwZGF0ZUFwcFN0YXRlKHsgaXNQYXVzZWQ6IGZhbHNlIH0pXG4gIH1cblxuICBjb25zdCBoYW5kbGVTdG9wUmVjb3JkaW5nID0gKCkgPT4ge1xuICAgIC8vIFN0b3Agd2Vic29ja2V0IHJlY29yZGluZ1xuICAgIHdzU3RvcFJlY29yZGluZygpXG5cbiAgICB1cGRhdGVBcHBTdGF0ZSh7XG4gICAgICBpc1JlY29yZGluZzogZmFsc2UsXG4gICAgICBpc1BhdXNlZDogZmFsc2UsXG4gICAgfSlcblxuICAgIC8vIE5hdmlnYXRlIGRpcmVjdGx5IHRvIHRyYW5zY3JpcHRpb24gc2NyZWVuIHdpdGggcmVhbCBkYXRhXG4gICAgb25OYXZpZ2F0ZShcInRyYW5zY3JpcHRpb24tc2NyZWVuXCIpXG4gIH1cblxuICAvLyBVc2UgcmVhbCB0cmFuc2NyaXB0aW9uIGRhdGEgb3IgZmFsbGJhY2sgdG8gZW1wdHkgc3RhdGVcbiAgY29uc3QgY3VycmVudFRyYW5zY3JpcHREYXRhID0gdHJhbnNjcmlwdERhdGEgfHwgYXBwU3RhdGUudHJhbnNjcmlwdERhdGFcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS04IGZsZXggZmxleC1jb2wgaC1zY3JlZW5cIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTggcGItNCBib3JkZXItYiBmbGV4LXNocmluay0wXCI+XG4gICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkXCI+UmVjb3JkaW5nIEludGVydmlldzwvaDI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTRcIj5cbiAgICAgICAgICB7LyogQ29ubmVjdGlvbiBTdGF0dXMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAge2lzQ29ubmVjdGVkID8gKFxuICAgICAgICAgICAgICA8V2lmaSBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JlZW4tNTAwXCIgLz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxXaWZpT2ZmIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1yZWQtNTAwXCIgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICB7aXNDb25uZWN0ZWQgPyAnQ29ubmVjdGVkJyA6ICdEaXNjb25uZWN0ZWQnfVxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFJlY29yZGluZyBTdGF0dXMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0zIGJnLXJlZC01MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2VcIj48L2Rpdj5cbiAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwic2Vjb25kYXJ5XCI+e3JlY29yZGluZ1RpbWV9PC9CYWRnZT5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEVycm9yIEFsZXJ0ICovfVxuICAgICAge3dzRXJyb3IgJiYgKFxuICAgICAgICA8QWxlcnQgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICA8QWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIHt3c0Vycm9yfVxuICAgICAgICAgIDwvQWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgPC9BbGVydD5cbiAgICAgICl9XG5cbiAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm1iLTggZmxleC1zaHJpbmstMFwiPlxuICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgIDxwPlxuICAgICAgICAgICAgICA8c3Ryb25nPkNhc2U6PC9zdHJvbmc+IHthcHBTdGF0ZS5jdXJyZW50Q2FzZT8uaWR9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8cD5cbiAgICAgICAgICAgICAgPHN0cm9uZz5XaXRuZXNzOjwvc3Ryb25nPiB7YXBwU3RhdGUuY3VycmVudFdpdG5lc3M/Lm5hbWV9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICA8L0NhcmQ+XG5cbiAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm1iLTggZmxleC0xXCI+XG4gICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC1oLTk2IG92ZXJmbG93LXktYXV0byBwLTRcIj5cbiAgICAgICAgICAgIHtjdXJyZW50VHJhbnNjcmlwdERhdGE/LnNlZ21lbnRzICYmIGN1cnJlbnRUcmFuc2NyaXB0RGF0YS5zZWdtZW50cy5sZW5ndGggPiAwID8gKFxuICAgICAgICAgICAgICBjdXJyZW50VHJhbnNjcmlwdERhdGEuc2VnbWVudHMubWFwKChzZWdtZW50LCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IHNwZWFrZXIgPSBjdXJyZW50VHJhbnNjcmlwdERhdGEuc3BlYWtlcnMuZmluZCgocykgPT4gcy5pZCA9PT0gc2VnbWVudC5zcGVha2VyKVxuICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImZsZXggZ2FwLTQgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWluLXctWzEyMHB4XSB0ZXh0LXJpZ2h0IHByLTMgYm9yZGVyLXItMiB0ZXh0LXNtIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBib3JkZXJSaWdodENvbG9yOiBzcGVha2VyPy5jb2xvciB9fVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge3NwZWFrZXI/Lm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb250LW5vcm1hbFwiPntzZWdtZW50LnRpbWVzdGFtcH08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtc20gbGVhZGluZy1yZWxheGVkXCI+e3NlZ21lbnQudGV4dH08L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIHB5LThcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtYi0yXCI+V2FpdGluZyBmb3IgdHJhbnNjcmlwdGlvbi4uLjwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICB7aXNDb25uZWN0ZWQgPyAnQ29ubmVjdGVkIHRvIHRyYW5zY3JpcHRpb24gc2VydmljZScgOiAnQ29ubmVjdGluZyB0byB0cmFuc2NyaXB0aW9uIHNlcnZpY2UuLi4nfVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgPC9DYXJkPlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgZ2FwLTQgbWF4LXctc20gbXgtYXV0byBmbGV4LXNocmluay0wXCI+XG4gICAgICAgIHshYXBwU3RhdGUuaXNQYXVzZWQgPyAoXG4gICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwic2Vjb25kYXJ5XCIgc2l6ZT1cImxnXCIgb25DbGljaz17aGFuZGxlUGF1c2VSZWNvcmRpbmd9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICA8UGF1c2UgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICBQYXVzZVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cInNlY29uZGFyeVwiIHNpemU9XCJsZ1wiIG9uQ2xpY2s9e2hhbmRsZVJlc3VtZVJlY29yZGluZ30gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgIDxQbGF5IGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgUmVzdW1lXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICl9XG5cbiAgICAgICAgPEJ1dHRvbiBzaXplPVwibGdcIiBvbkNsaWNrPXtoYW5kbGVTdG9wUmVjb3JkaW5nfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgIDxTcXVhcmUgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgU3RvcCBJbnRlcnZpZXdcbiAgICAgICAgPC9CdXR0b24+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQnV0dG9uIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQmFkZ2UiLCJBbGVydCIsIkFsZXJ0RGVzY3JpcHRpb24iLCJQYXVzZSIsIlBsYXkiLCJTcXVhcmUiLCJXaWZpIiwiV2lmaU9mZiIsIkFsZXJ0Q2lyY2xlIiwidXNlV2ViU29ja2V0UmVjb3JkaW5nIiwiV0VCU09DS0VUX0VORFBPSU5UIiwiUmVjb3JkaW5nU2NyZWVuIiwib25OYXZpZ2F0ZSIsImFwcFN0YXRlIiwidXBkYXRlQXBwU3RhdGUiLCJyZWNvcmRpbmdUaW1lIiwic2V0UmVjb3JkaW5nVGltZSIsInN0YXJ0VGltZSIsInNldFN0YXJ0VGltZSIsImlzQ29ubmVjdGVkIiwiaXNSZWNvcmRpbmciLCJ3c0lzUmVjb3JkaW5nIiwidHJhbnNjcmlwdERhdGEiLCJlcnJvciIsIndzRXJyb3IiLCJzdGFydFJlY29yZGluZyIsIndzU3RhcnRSZWNvcmRpbmciLCJzdG9wUmVjb3JkaW5nIiwid3NTdG9wUmVjb3JkaW5nIiwicGF1c2VSZWNvcmRpbmciLCJ3c1BhdXNlUmVjb3JkaW5nIiwicmVzdW1lUmVjb3JkaW5nIiwid3NSZXN1bWVSZWNvcmRpbmciLCJ3ZWJzb2NrZXRVcmwiLCJvblRyYW5zY3JpcHRpb25VcGRhdGUiLCJkYXRhIiwib25FcnJvciIsImNvbnNvbGUiLCJvbkNvbm5lY3Rpb25DaGFuZ2UiLCJjb25uZWN0ZWQiLCJsb2ciLCJoYW5kbGVTdGFydFJlY29yZGluZyIsImludGVydmFsIiwiaXNQYXVzZWQiLCJzZXRJbnRlcnZhbCIsImVsYXBzZWQiLCJEYXRlIiwibm93IiwibWludXRlcyIsIk1hdGgiLCJmbG9vciIsInNlY29uZHMiLCJ0b1N0cmluZyIsInBhZFN0YXJ0IiwiY2xlYXJJbnRlcnZhbCIsInJlY29yZGluZ1N0YXJ0VGltZSIsImhhbmRsZVBhdXNlUmVjb3JkaW5nIiwiaGFuZGxlUmVzdW1lUmVjb3JkaW5nIiwiaGFuZGxlU3RvcFJlY29yZGluZyIsImN1cnJlbnRUcmFuc2NyaXB0RGF0YSIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwic3BhbiIsInZhcmlhbnQiLCJwIiwic3Ryb25nIiwiY3VycmVudENhc2UiLCJpZCIsImN1cnJlbnRXaXRuZXNzIiwibmFtZSIsInNlZ21lbnRzIiwibGVuZ3RoIiwibWFwIiwic2VnbWVudCIsImluZGV4Iiwic3BlYWtlciIsInNwZWFrZXJzIiwiZmluZCIsInMiLCJzdHlsZSIsImJvcmRlclJpZ2h0Q29sb3IiLCJjb2xvciIsInRpbWVzdGFtcCIsInRleHQiLCJzaXplIiwib25DbGljayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/recording-screen.tsx\n"));

/***/ })

});