"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/recording-screen.tsx":
/*!*****************************************!*\
  !*** ./components/recording-screen.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecordingScreen: () => (/* binding */ RecordingScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Pause,Play,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Pause,Play,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Pause,Play,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Pause,Play,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Pause,Play,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Pause,Play,Square,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _hooks_use_websocket_recording__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-websocket-recording */ \"(app-pages-browser)/./hooks/use-websocket-recording.ts\");\n/* __next_internal_client_entry_do_not_use__ RecordingScreen auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// TODO: Replace with your actual websocket endpoint\nconst WEBSOCKET_ENDPOINT = \"ws://localhost:8080/ws/transcribe\" // Replace this with your actual endpoint\n;\nfunction RecordingScreen(param) {\n    let { onNavigate, appState, updateAppState } = param;\n    var _appState_currentCase, _appState_currentWitness;\n    _s();\n    const [recordingTime, setRecordingTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"00:00\");\n    const [startTime, setStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isPending, startTransition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useTransition)();\n    // Initialize websocket recording\n    const { isConnected, isRecording: wsIsRecording, transcriptData, error: wsError, startRecording: wsStartRecording, stopRecording: wsStopRecording, pauseRecording: wsPauseRecording, resumeRecording: wsResumeRecording } = (0,_hooks_use_websocket_recording__WEBPACK_IMPORTED_MODULE_6__.useWebSocketRecording)({\n        websocketUrl: WEBSOCKET_ENDPOINT,\n        onTranscriptionUpdate: {\n            \"RecordingScreen.useWebSocketRecording\": (data)=>{\n                // Update app state with new transcription data\n                startTransition({\n                    \"RecordingScreen.useWebSocketRecording\": ()=>updateAppState({\n                            transcriptData: data\n                        })\n                }[\"RecordingScreen.useWebSocketRecording\"]);\n            }\n        }[\"RecordingScreen.useWebSocketRecording\"],\n        onError: {\n            \"RecordingScreen.useWebSocketRecording\": (error)=>{\n                console.error('WebSocket recording error:', error);\n            }\n        }[\"RecordingScreen.useWebSocketRecording\"],\n        onConnectionChange: {\n            \"RecordingScreen.useWebSocketRecording\": (connected)=>{\n                console.log('WebSocket connection status:', connected);\n            }\n        }[\"RecordingScreen.useWebSocketRecording\"]\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecordingScreen.useEffect\": ()=>{\n            // Auto-start recording when component mounts\n            if (!appState.isRecording && !wsIsRecording) {\n                handleStartRecording();\n            }\n        }\n    }[\"RecordingScreen.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecordingScreen.useEffect\": ()=>{\n            let interval = null;\n            if (appState.isRecording && !appState.isPaused && startTime) {\n                interval = setInterval({\n                    \"RecordingScreen.useEffect\": ()=>{\n                        const elapsed = Date.now() - startTime;\n                        const minutes = Math.floor(elapsed / 60000);\n                        const seconds = Math.floor(elapsed % 60000 / 1000);\n                        setRecordingTime(\"\".concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\")));\n                    }\n                }[\"RecordingScreen.useEffect\"], 1000);\n            }\n            return ({\n                \"RecordingScreen.useEffect\": ()=>{\n                    if (interval) clearInterval(interval);\n                }\n            })[\"RecordingScreen.useEffect\"];\n        }\n    }[\"RecordingScreen.useEffect\"], [\n        appState.isRecording,\n        appState.isPaused,\n        startTime\n    ]);\n    const handleStartRecording = async ()=>{\n        try {\n            const now = Date.now();\n            setStartTime(now);\n            // Start websocket recording\n            await wsStartRecording();\n            updateAppState({\n                isRecording: true,\n                isPaused: false,\n                recordingStartTime: now\n            });\n        } catch (error) {\n            console.error('Failed to start recording:', error);\n        }\n    };\n    const handlePauseRecording = ()=>{\n        wsPauseRecording();\n        updateAppState({\n            isPaused: true\n        });\n    };\n    const handleResumeRecording = ()=>{\n        wsResumeRecording();\n        updateAppState({\n            isPaused: false\n        });\n    };\n    const handleStopRecording = ()=>{\n        // Stop websocket recording\n        wsStopRecording();\n        updateAppState({\n            isRecording: false,\n            isPaused: false\n        });\n        // Navigate directly to transcription screen with real data\n        onNavigate(\"transcription-screen\");\n    };\n    // Use real transcription data or fallback to empty state\n    const currentTranscriptData = transcriptData || appState.transcriptData;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8 flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-8 pb-4 border-b flex-shrink-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold\",\n                        children: \"Recording Interview\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: isConnected ? 'Connected' : 'Disconnected'\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        children: recordingTime\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            wsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                        children: wsError\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-8 flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Case:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    (_appState_currentCase = appState.currentCase) === null || _appState_currentCase === void 0 ? void 0 : _appState_currentCase.id\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Witness:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    (_appState_currentWitness = appState.currentWitness) === null || _appState_currentWitness === void 0 ? void 0 : _appState_currentWitness.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-8 flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto p-4\",\n                        children: (currentTranscriptData === null || currentTranscriptData === void 0 ? void 0 : currentTranscriptData.segments) && currentTranscriptData.segments.length > 0 ? currentTranscriptData.segments.map((segment, index)=>{\n                            const speaker = currentTranscriptData.speakers.find((s)=>s.id === segment.speaker);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"min-w-[120px] text-right pr-3 border-r-2 text-sm font-medium\",\n                                        style: {\n                                            borderRightColor: speaker === null || speaker === void 0 ? void 0 : speaker.color\n                                        },\n                                        children: [\n                                            speaker === null || speaker === void 0 ? void 0 : speaker.name,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-muted-foreground font-normal\",\n                                                children: segment.timestamp\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-sm leading-relaxed\",\n                                        children: segment.text\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 19\n                            }, this);\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-2\",\n                                    children: \"Waiting for transcription...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: isConnected ? 'Connected to transcription service' : 'Connecting to transcription service...'\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col gap-4 max-w-sm mx-auto flex-shrink-0\",\n                children: [\n                    !appState.isPaused ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"secondary\",\n                        size: \"lg\",\n                        onClick: handlePauseRecording,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this),\n                            \"Pause\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"secondary\",\n                        size: \"lg\",\n                        onClick: handleResumeRecording,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            \"Resume\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        size: \"lg\",\n                        onClick: handleStopRecording,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Pause_Play_Square_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            \"Stop Interview\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/dev/scdf/components/recording-screen.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_s(RecordingScreen, \"mNtB4Km3HIdESDkGCobXkl7yxx0=\", false, function() {\n    return [\n        react__WEBPACK_IMPORTED_MODULE_1__.useTransition,\n        _hooks_use_websocket_recording__WEBPACK_IMPORTED_MODULE_6__.useWebSocketRecording\n    ];\n});\n_c = RecordingScreen;\nvar _c;\n$RefreshReg$(_c, \"RecordingScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/recording-screen.tsx\n"));

/***/ })

});