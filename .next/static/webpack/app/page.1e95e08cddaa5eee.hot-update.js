"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./hooks/use-websocket-recording.ts":
/*!******************************************!*\
  !*** ./hooks/use-websocket-recording.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWebSocketRecording: () => (/* binding */ useWebSocketRecording)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useWebSocketRecording auto */ \nfunction useWebSocketRecording(config) {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [transcriptData, setTranscriptData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isPaused, setIsPaused] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const wsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const streamRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const processorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { websocketUrl, sampleRate = 16000, bufferSize = 4096, onTranscriptionUpdate, onError, onConnectionChange } = config;\n    // Initialize default speakers\n    const defaultSpeakers = [\n        {\n            id: \"speaker_1\",\n            name: \"Officer\",\n            type: \"officer\",\n            color: \"#2563eb\"\n        },\n        {\n            id: \"speaker_2\",\n            name: \"Witness\",\n            type: \"witness\",\n            color: \"#dc3545\"\n        }\n    ];\n    // Initialize WebSocket connection\n    const connectWebSocket = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWebSocketRecording.useCallback[connectWebSocket]\": ()=>{\n            try {\n                var _wsRef_current;\n                if (((_wsRef_current = wsRef.current) === null || _wsRef_current === void 0 ? void 0 : _wsRef_current.readyState) === WebSocket.OPEN) {\n                    return;\n                }\n                wsRef.current = new WebSocket(websocketUrl);\n                wsRef.current.onopen = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        setError(null);\n                        onConnectionChange === null || onConnectionChange === void 0 ? void 0 : onConnectionChange(true);\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n                wsRef.current.onmessage = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": (event)=>{\n                        try {\n                            const data = JSON.parse(event.data);\n                            // Handle different message types from the server\n                            if (data.text) {\n                                const newSegment = {\n                                    speaker: data.speaker,\n                                    timestamp: data.end_time,\n                                    text: data.text || ''\n                                };\n                                setTranscriptData({\n                                    \"useWebSocketRecording.useCallback[connectWebSocket]\": (prev)=>{\n                                        let updated = {\n                                            speakers: (prev === null || prev === void 0 ? void 0 : prev.speakers) || defaultSpeakers,\n                                            segments: (prev === null || prev === void 0 ? void 0 : prev.segments) || []\n                                        };\n                                        if (!prev) {\n                                            updated = {\n                                                speakers: defaultSpeakers,\n                                                segments: [\n                                                    newSegment\n                                                ]\n                                            };\n                                        } else if (prev && prev.segments[prev.segments.length - 1].speaker === newSegment.speaker) {\n                                            updated = {\n                                                ...prev\n                                            };\n                                            updated.segments[updated.segments.length - 1].text = updated.segments[updated.segments.length - 1].text + ' ' + newSegment.text;\n                                            updated.segments[updated.segments.length - 1].timestamp = newSegment.timestamp;\n                                        }\n                                        onTranscriptionUpdate === null || onTranscriptionUpdate === void 0 ? void 0 : onTranscriptionUpdate(updated);\n                                        return updated;\n                                    }\n                                }[\"useWebSocketRecording.useCallback[connectWebSocket]\"]);\n                            }\n                        // else if (data.text === 'partial_transcription') {\n                        //   // Handle partial/interim transcription results\n                        //   setTranscriptData(prev => {\n                        //     if (!prev) return null;\n                        //     const segments = [...prev.segments];\n                        //     const lastSegment = segments[segments.length - 1];\n                        //     if (lastSegment && data.is_partial) {\n                        //       // Update the last segment with partial text\n                        //       segments[segments.length - 1] = {\n                        //         ...lastSegment,\n                        //         text: data.text || lastSegment.text\n                        //       };\n                        //     }\n                        //     const updated = { ...prev, segments };\n                        //     onTranscriptionUpdate?.(updated);\n                        //     return updated;\n                        //   });\n                        // } \n                        // else if (data.type === 'error') {\n                        //   const errorMsg = data.message || 'Transcription error occurred';\n                        //   setError(errorMsg);\n                        //   onError?.(errorMsg);\n                        // }\n                        } catch (err) {\n                            console.error('Error parsing WebSocket message:', err);\n                            setError('Failed to parse transcription data');\n                        }\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n                wsRef.current.onerror = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": (event)=>{\n                        console.error('WebSocket error:', event);\n                        setError('WebSocket connection error');\n                        onError === null || onError === void 0 ? void 0 : onError('WebSocket connection error');\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n                wsRef.current.onclose = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": ()=>{\n                        console.log('WebSocket disconnected');\n                        setIsConnected(false);\n                        onConnectionChange === null || onConnectionChange === void 0 ? void 0 : onConnectionChange(false);\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n            } catch (err) {\n                console.error('Failed to connect WebSocket:', err);\n                setError('Failed to connect to transcription service');\n                onError === null || onError === void 0 ? void 0 : onError('Failed to connect to transcription service');\n            }\n        }\n    }[\"useWebSocketRecording.useCallback[connectWebSocket]\"], [\n        websocketUrl,\n        onTranscriptionUpdate,\n        onError,\n        onConnectionChange\n    ]);\n    // Format timestamp helper\n    const formatTimestamp = (timestamp)=>{\n        const date = new Date(timestamp);\n        const minutes = date.getMinutes().toString().padStart(2, '0');\n        const seconds = date.getSeconds().toString().padStart(2, '0');\n        return \"\".concat(minutes, \":\").concat(seconds);\n    };\n    // Initialize audio recording\n    const initializeAudio = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: sampleRate,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            streamRef.current = stream;\n            return stream;\n        } catch (err) {\n            throw new Error('Failed to access microphone');\n        }\n    };\n    // Send audio data to WebSocket\n    const sendAudioData = (audioData)=>{\n        var _wsRef_current;\n        if (((_wsRef_current = wsRef.current) === null || _wsRef_current === void 0 ? void 0 : _wsRef_current.readyState) === WebSocket.OPEN && !isPaused) {\n            // Convert Float32Array to Int16Array for better compression\n            const int16Data = new Int16Array(audioData.length);\n            for(let i = 0; i < audioData.length; i++){\n                int16Data[i] = Math.max(-32768, Math.min(32767, audioData[i] * 32768));\n            }\n            // Send as binary data\n            wsRef.current.send(int16Data.buffer);\n        }\n    };\n    // Start recording\n    const startRecording = async ()=>{\n        try {\n            setError(null);\n            // Connect WebSocket if not connected\n            if (!isConnected) {\n                connectWebSocket();\n                // Wait a bit for connection\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            }\n            // Initialize audio\n            const stream = await initializeAudio();\n            // Create audio context for processing\n            audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)({\n                sampleRate: sampleRate\n            });\n            const source = audioContextRef.current.createMediaStreamSource(stream);\n            processorRef.current = audioContextRef.current.createScriptProcessor(bufferSize, 1, 1);\n            processorRef.current.onaudioprocess = (event)=>{\n                const inputData = event.inputBuffer.getChannelData(0);\n                sendAudioData(inputData);\n            };\n            source.connect(processorRef.current);\n            processorRef.current.connect(audioContextRef.current.destination);\n            setIsRecording(true);\n            setIsPaused(false);\n            // Initialize transcript data if not exists\n            if (!transcriptData) {\n                setTranscriptData({\n                    speakers: defaultSpeakers,\n                    segments: []\n                });\n            }\n        } catch (err) {\n            const errorMsg = err instanceof Error ? err.message : 'Failed to start recording';\n            setError(errorMsg);\n            onError === null || onError === void 0 ? void 0 : onError(errorMsg);\n            throw err;\n        }\n    };\n    // Stop recording\n    const stopRecording = ()=>{\n        setIsRecording(false);\n        setIsPaused(false);\n        // Stop audio processing\n        if (processorRef.current) {\n            processorRef.current.disconnect();\n            processorRef.current = null;\n        }\n        if (audioContextRef.current) {\n            audioContextRef.current.close();\n            audioContextRef.current = null;\n        }\n        if (streamRef.current) {\n            streamRef.current.getTracks().forEach((track)=>track.stop());\n            streamRef.current = null;\n        }\n        // Close WebSocket\n        if (wsRef.current) {\n            wsRef.current.close();\n            wsRef.current = null;\n        }\n    };\n    // Pause recording\n    const pauseRecording = ()=>{\n        setIsPaused(true);\n    };\n    // Resume recording\n    const resumeRecording = ()=>{\n        setIsPaused(false);\n    };\n    // Clear transcript\n    const clearTranscript = ()=>{\n        setTranscriptData({\n            speakers: defaultSpeakers,\n            segments: []\n        });\n    };\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWebSocketRecording.useEffect\": ()=>{\n            return ({\n                \"useWebSocketRecording.useEffect\": ()=>{\n                    stopRecording();\n                }\n            })[\"useWebSocketRecording.useEffect\"];\n        }\n    }[\"useWebSocketRecording.useEffect\"], []);\n    return {\n        isConnected,\n        isRecording,\n        transcriptData,\n        error,\n        startRecording,\n        stopRecording,\n        pauseRecording,\n        resumeRecording,\n        clearTranscript\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/use-websocket-recording.ts\n"));

/***/ })

});