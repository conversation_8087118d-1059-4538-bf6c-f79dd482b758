"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./hooks/use-websocket-recording.ts":
/*!******************************************!*\
  !*** ./hooks/use-websocket-recording.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWebSocketRecording: () => (/* binding */ useWebSocketRecording)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useWebSocketRecording auto */ \nfunction useWebSocketRecording(config) {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [transcriptData, setTranscriptData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isPaused, setIsPaused] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const wsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const streamRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const processorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { websocketUrl, sampleRate = 16000, bufferSize = 4096, onTranscriptionUpdate, onError, onConnectionChange } = config;\n    // Initialize default speakers\n    const defaultSpeakers = [\n        {\n            id: \"speaker_1\",\n            name: \"Officer\",\n            type: \"officer\",\n            color: \"#2563eb\"\n        },\n        {\n            id: \"speaker_2\",\n            name: \"Witness\",\n            type: \"witness\",\n            color: \"#dc3545\"\n        }\n    ];\n    // Initialize WebSocket connection\n    const connectWebSocket = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWebSocketRecording.useCallback[connectWebSocket]\": ()=>{\n            try {\n                var _wsRef_current;\n                if (((_wsRef_current = wsRef.current) === null || _wsRef_current === void 0 ? void 0 : _wsRef_current.readyState) === WebSocket.OPEN) {\n                    return;\n                }\n                wsRef.current = new WebSocket(websocketUrl);\n                wsRef.current.onopen = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        setError(null);\n                        onConnectionChange === null || onConnectionChange === void 0 ? void 0 : onConnectionChange(true);\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n                wsRef.current.onmessage = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": (event)=>{\n                        try {\n                            const data = JSON.parse(event.data);\n                            // Handle different message types from the server\n                            if (data.text) {\n                                const newSegment = {\n                                    speaker: data.speaker,\n                                    timestamp: data.end_time,\n                                    text: data.text || ''\n                                };\n                                setTranscriptData({\n                                    \"useWebSocketRecording.useCallback[connectWebSocket]\": (prev)=>{\n                                        let updated = {\n                                            speakers: (prev === null || prev === void 0 ? void 0 : prev.speakers) || defaultSpeakers,\n                                            segments: (prev === null || prev === void 0 ? void 0 : prev.segments) || []\n                                        };\n                                        console.log('-----', prev);\n                                        if (!prev) {\n                                            updated = {\n                                                speakers: defaultSpeakers,\n                                                segments: [\n                                                    newSegment\n                                                ]\n                                            };\n                                        } else if (prev && prev.segments.length > 0 && prev.segments[prev.segments.length - 1].speaker === newSegment.speaker) {\n                                            updated = {\n                                                ...prev\n                                            };\n                                            updated.segments[updated.segments.length - 1].text = updated.segments[updated.segments.length - 1].text + ' ' + newSegment.text;\n                                            updated.segments[updated.segments.length - 1].timestamp = newSegment.timestamp;\n                                        } else {\n                                            updated.segments.push(newSegment);\n                                        }\n                                        onTranscriptionUpdate === null || onTranscriptionUpdate === void 0 ? void 0 : onTranscriptionUpdate(updated);\n                                        return updated;\n                                    }\n                                }[\"useWebSocketRecording.useCallback[connectWebSocket]\"]);\n                            }\n                        // else if (data.text === 'partial_transcription') {\n                        //   // Handle partial/interim transcription results\n                        //   setTranscriptData(prev => {\n                        //     if (!prev) return null;\n                        //     const segments = [...prev.segments];\n                        //     const lastSegment = segments[segments.length - 1];\n                        //     if (lastSegment && data.is_partial) {\n                        //       // Update the last segment with partial text\n                        //       segments[segments.length - 1] = {\n                        //         ...lastSegment,\n                        //         text: data.text || lastSegment.text\n                        //       };\n                        //     }\n                        //     const updated = { ...prev, segments };\n                        //     onTranscriptionUpdate?.(updated);\n                        //     return updated;\n                        //   });\n                        // } \n                        // else if (data.type === 'error') {\n                        //   const errorMsg = data.message || 'Transcription error occurred';\n                        //   setError(errorMsg);\n                        //   onError?.(errorMsg);\n                        // }\n                        } catch (err) {\n                            console.error('Error parsing WebSocket message:', err);\n                            setError('Failed to parse transcription data');\n                        }\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n                wsRef.current.onerror = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": (event)=>{\n                        console.error('WebSocket error:', event);\n                        setError('WebSocket connection error');\n                        onError === null || onError === void 0 ? void 0 : onError('WebSocket connection error');\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n                wsRef.current.onclose = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": ()=>{\n                        console.log('WebSocket disconnected');\n                        setIsConnected(false);\n                        onConnectionChange === null || onConnectionChange === void 0 ? void 0 : onConnectionChange(false);\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n            } catch (err) {\n                console.error('Failed to connect WebSocket:', err);\n                setError('Failed to connect to transcription service');\n                onError === null || onError === void 0 ? void 0 : onError('Failed to connect to transcription service');\n            }\n        }\n    }[\"useWebSocketRecording.useCallback[connectWebSocket]\"], [\n        websocketUrl,\n        onTranscriptionUpdate,\n        onError,\n        onConnectionChange\n    ]);\n    // Format timestamp helper\n    const formatTimestamp = (timestamp)=>{\n        const date = new Date(timestamp);\n        const minutes = date.getMinutes().toString().padStart(2, '0');\n        const seconds = date.getSeconds().toString().padStart(2, '0');\n        return \"\".concat(minutes, \":\").concat(seconds);\n    };\n    // Initialize audio recording\n    const initializeAudio = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: sampleRate,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            streamRef.current = stream;\n            return stream;\n        } catch (err) {\n            throw new Error('Failed to access microphone');\n        }\n    };\n    // Send audio data to WebSocket\n    const sendAudioData = (audioData)=>{\n        var _wsRef_current;\n        if (((_wsRef_current = wsRef.current) === null || _wsRef_current === void 0 ? void 0 : _wsRef_current.readyState) === WebSocket.OPEN && !isPaused) {\n            // Convert Float32Array to Int16Array for better compression\n            const int16Data = new Int16Array(audioData.length);\n            for(let i = 0; i < audioData.length; i++){\n                int16Data[i] = Math.max(-32768, Math.min(32767, audioData[i] * 32768));\n            }\n            // Send as binary data\n            wsRef.current.send(int16Data.buffer);\n        }\n    };\n    // Start recording\n    const startRecording = async ()=>{\n        try {\n            setError(null);\n            // Connect WebSocket if not connected\n            if (!isConnected) {\n                connectWebSocket();\n                // Wait a bit for connection\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            }\n            // Initialize audio\n            const stream = await initializeAudio();\n            // Create audio context for processing\n            audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)({\n                sampleRate: sampleRate\n            });\n            const source = audioContextRef.current.createMediaStreamSource(stream);\n            processorRef.current = audioContextRef.current.createScriptProcessor(bufferSize, 1, 1);\n            processorRef.current.onaudioprocess = (event)=>{\n                const inputData = event.inputBuffer.getChannelData(0);\n                sendAudioData(inputData);\n            };\n            source.connect(processorRef.current);\n            processorRef.current.connect(audioContextRef.current.destination);\n            setIsRecording(true);\n            setIsPaused(false);\n            // Initialize transcript data if not exists\n            if (!transcriptData) {\n                setTranscriptData({\n                    speakers: defaultSpeakers,\n                    segments: []\n                });\n            }\n        } catch (err) {\n            const errorMsg = err instanceof Error ? err.message : 'Failed to start recording';\n            setError(errorMsg);\n            onError === null || onError === void 0 ? void 0 : onError(errorMsg);\n            throw err;\n        }\n    };\n    // Stop recording\n    const stopRecording = ()=>{\n        setIsRecording(false);\n        setIsPaused(false);\n        // Stop audio processing\n        if (processorRef.current) {\n            processorRef.current.disconnect();\n            processorRef.current = null;\n        }\n        if (audioContextRef.current) {\n            audioContextRef.current.close();\n            audioContextRef.current = null;\n        }\n        if (streamRef.current) {\n            streamRef.current.getTracks().forEach((track)=>track.stop());\n            streamRef.current = null;\n        }\n        // Close WebSocket\n        if (wsRef.current) {\n            wsRef.current.close();\n            wsRef.current = null;\n        }\n    };\n    // Pause recording\n    const pauseRecording = ()=>{\n        setIsPaused(true);\n    };\n    // Resume recording\n    const resumeRecording = ()=>{\n        setIsPaused(false);\n    };\n    // Clear transcript\n    const clearTranscript = ()=>{\n        setTranscriptData({\n            speakers: defaultSpeakers,\n            segments: []\n        });\n    };\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWebSocketRecording.useEffect\": ()=>{\n            return ({\n                \"useWebSocketRecording.useEffect\": ()=>{\n                    stopRecording();\n                }\n            })[\"useWebSocketRecording.useEffect\"];\n        }\n    }[\"useWebSocketRecording.useEffect\"], []);\n    return {\n        isConnected,\n        isRecording,\n        transcriptData,\n        error,\n        startRecording,\n        stopRecording,\n        pauseRecording,\n        resumeRecording,\n        clearTranscript\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/use-websocket-recording.ts\n"));

/***/ })

});