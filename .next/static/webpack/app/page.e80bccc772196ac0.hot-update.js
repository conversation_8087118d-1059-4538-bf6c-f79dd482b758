"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./hooks/use-websocket-recording.ts":
/*!******************************************!*\
  !*** ./hooks/use-websocket-recording.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWebSocketRecording: () => (/* binding */ useWebSocketRecording)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useWebSocketRecording auto */ \nfunction useWebSocketRecording(config) {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [transcriptData, setTranscriptData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isPaused, setIsPaused] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const wsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const streamRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const processorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { websocketUrl, sampleRate = 16000, bufferSize = 4096, onTranscriptionUpdate, onError, onConnectionChange } = config;\n    // Initialize default speakers\n    const defaultSpeakers = [\n        {\n            id: \"speaker_1\",\n            name: \"Officer\",\n            type: \"officer\",\n            color: \"#2563eb\"\n        },\n        {\n            id: \"speaker_2\",\n            name: \"Witness\",\n            type: \"witness\",\n            color: \"#dc3545\"\n        }\n    ];\n    // Initialize WebSocket connection\n    const connectWebSocket = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWebSocketRecording.useCallback[connectWebSocket]\": ()=>{\n            try {\n                var _wsRef_current;\n                if (((_wsRef_current = wsRef.current) === null || _wsRef_current === void 0 ? void 0 : _wsRef_current.readyState) === WebSocket.OPEN) {\n                    return;\n                }\n                wsRef.current = new WebSocket(websocketUrl);\n                wsRef.current.onopen = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        setError(null);\n                        onConnectionChange === null || onConnectionChange === void 0 ? void 0 : onConnectionChange(true);\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n                wsRef.current.onmessage = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": (event)=>{\n                        try {\n                            const data = JSON.parse(event.data);\n                            // Handle different message types from the server\n                            if (data.text) {\n                                const newSegment = {\n                                    speaker: data.speaker,\n                                    timestamp: data.end_time,\n                                    text: data.text || ''\n                                };\n                                setTranscriptData({\n                                    \"useWebSocketRecording.useCallback[connectWebSocket]\": (prev)=>{\n                                        let updated = {\n                                            speakers: (prev === null || prev === void 0 ? void 0 : prev.speakers) || defaultSpeakers,\n                                            segments: (prev === null || prev === void 0 ? void 0 : prev.segments) || []\n                                        };\n                                        if (!prev) {\n                                            updated = {\n                                                speakers: defaultSpeakers,\n                                                segments: [\n                                                    newSegment\n                                                ]\n                                            };\n                                        } else if (prev && prev.segments[prev.segments.length - 1].speaker === newSegment.speaker) {\n                                            let updated = {\n                                                ...prev\n                                            };\n                                            updated.segments[updated.segments.length - 1].text = updated.segments[updated.segments.length - 1].text + ' ' + newSegment.text;\n                                        }\n                                        onTranscriptionUpdate === null || onTranscriptionUpdate === void 0 ? void 0 : onTranscriptionUpdate(updated);\n                                        return updated;\n                                    }\n                                }[\"useWebSocketRecording.useCallback[connectWebSocket]\"]);\n                            }\n                        // else if (data.text === 'partial_transcription') {\n                        //   // Handle partial/interim transcription results\n                        //   setTranscriptData(prev => {\n                        //     if (!prev) return null;\n                        //     const segments = [...prev.segments];\n                        //     const lastSegment = segments[segments.length - 1];\n                        //     if (lastSegment && data.is_partial) {\n                        //       // Update the last segment with partial text\n                        //       segments[segments.length - 1] = {\n                        //         ...lastSegment,\n                        //         text: data.text || lastSegment.text\n                        //       };\n                        //     }\n                        //     const updated = { ...prev, segments };\n                        //     onTranscriptionUpdate?.(updated);\n                        //     return updated;\n                        //   });\n                        // } \n                        // else if (data.type === 'error') {\n                        //   const errorMsg = data.message || 'Transcription error occurred';\n                        //   setError(errorMsg);\n                        //   onError?.(errorMsg);\n                        // }\n                        } catch (err) {\n                            console.error('Error parsing WebSocket message:', err);\n                            setError('Failed to parse transcription data');\n                        }\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n                wsRef.current.onerror = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": (event)=>{\n                        console.error('WebSocket error:', event);\n                        setError('WebSocket connection error');\n                        onError === null || onError === void 0 ? void 0 : onError('WebSocket connection error');\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n                wsRef.current.onclose = ({\n                    \"useWebSocketRecording.useCallback[connectWebSocket]\": ()=>{\n                        console.log('WebSocket disconnected');\n                        setIsConnected(false);\n                        onConnectionChange === null || onConnectionChange === void 0 ? void 0 : onConnectionChange(false);\n                    }\n                })[\"useWebSocketRecording.useCallback[connectWebSocket]\"];\n            } catch (err) {\n                console.error('Failed to connect WebSocket:', err);\n                setError('Failed to connect to transcription service');\n                onError === null || onError === void 0 ? void 0 : onError('Failed to connect to transcription service');\n            }\n        }\n    }[\"useWebSocketRecording.useCallback[connectWebSocket]\"], [\n        websocketUrl,\n        onTranscriptionUpdate,\n        onError,\n        onConnectionChange\n    ]);\n    // Format timestamp helper\n    const formatTimestamp = (timestamp)=>{\n        const date = new Date(timestamp);\n        const minutes = date.getMinutes().toString().padStart(2, '0');\n        const seconds = date.getSeconds().toString().padStart(2, '0');\n        return \"\".concat(minutes, \":\").concat(seconds);\n    };\n    // Initialize audio recording\n    const initializeAudio = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: sampleRate,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            streamRef.current = stream;\n            return stream;\n        } catch (err) {\n            throw new Error('Failed to access microphone');\n        }\n    };\n    // Send audio data to WebSocket\n    const sendAudioData = (audioData)=>{\n        var _wsRef_current;\n        if (((_wsRef_current = wsRef.current) === null || _wsRef_current === void 0 ? void 0 : _wsRef_current.readyState) === WebSocket.OPEN && !isPaused) {\n            // Convert Float32Array to Int16Array for better compression\n            const int16Data = new Int16Array(audioData.length);\n            for(let i = 0; i < audioData.length; i++){\n                int16Data[i] = Math.max(-32768, Math.min(32767, audioData[i] * 32768));\n            }\n            // Send as binary data\n            wsRef.current.send(int16Data.buffer);\n        }\n    };\n    // Start recording\n    const startRecording = async ()=>{\n        try {\n            setError(null);\n            // Connect WebSocket if not connected\n            if (!isConnected) {\n                connectWebSocket();\n                // Wait a bit for connection\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            }\n            // Initialize audio\n            const stream = await initializeAudio();\n            // Create audio context for processing\n            audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)({\n                sampleRate: sampleRate\n            });\n            const source = audioContextRef.current.createMediaStreamSource(stream);\n            processorRef.current = audioContextRef.current.createScriptProcessor(bufferSize, 1, 1);\n            processorRef.current.onaudioprocess = (event)=>{\n                const inputData = event.inputBuffer.getChannelData(0);\n                sendAudioData(inputData);\n            };\n            source.connect(processorRef.current);\n            processorRef.current.connect(audioContextRef.current.destination);\n            setIsRecording(true);\n            setIsPaused(false);\n            // Initialize transcript data if not exists\n            if (!transcriptData) {\n                setTranscriptData({\n                    speakers: defaultSpeakers,\n                    segments: []\n                });\n            }\n        } catch (err) {\n            const errorMsg = err instanceof Error ? err.message : 'Failed to start recording';\n            setError(errorMsg);\n            onError === null || onError === void 0 ? void 0 : onError(errorMsg);\n            throw err;\n        }\n    };\n    // Stop recording\n    const stopRecording = ()=>{\n        setIsRecording(false);\n        setIsPaused(false);\n        // Stop audio processing\n        if (processorRef.current) {\n            processorRef.current.disconnect();\n            processorRef.current = null;\n        }\n        if (audioContextRef.current) {\n            audioContextRef.current.close();\n            audioContextRef.current = null;\n        }\n        if (streamRef.current) {\n            streamRef.current.getTracks().forEach((track)=>track.stop());\n            streamRef.current = null;\n        }\n        // Close WebSocket\n        if (wsRef.current) {\n            wsRef.current.close();\n            wsRef.current = null;\n        }\n    };\n    // Pause recording\n    const pauseRecording = ()=>{\n        setIsPaused(true);\n    };\n    // Resume recording\n    const resumeRecording = ()=>{\n        setIsPaused(false);\n    };\n    // Clear transcript\n    const clearTranscript = ()=>{\n        setTranscriptData({\n            speakers: defaultSpeakers,\n            segments: []\n        });\n    };\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWebSocketRecording.useEffect\": ()=>{\n            return ({\n                \"useWebSocketRecording.useEffect\": ()=>{\n                    stopRecording();\n                }\n            })[\"useWebSocketRecording.useEffect\"];\n        }\n    }[\"useWebSocketRecording.useEffect\"], []);\n    return {\n        isConnected,\n        isRecording,\n        transcriptData,\n        error,\n        startRecording,\n        stopRecording,\n        pauseRecording,\n        resumeRecording,\n        clearTranscript\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/use-websocket-recording.ts\n"));

/***/ })

});