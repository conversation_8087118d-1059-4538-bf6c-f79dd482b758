"use client"

import { useState, useEffect, useTransition } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Pause, Play, Square, Wifi, WifiOff, AlertCircle } from "lucide-react"
import { useWebSocketRecording } from "@/hooks/use-websocket-recording"
import type { Screen, AppState } from "@/app/page"
import type { TranscriptData } from "@/types/database"

interface RecordingScreenProps {
  onNavigate: (screen: Screen) => void
  appState: AppState
  updateAppState: (updates: Partial<AppState>) => void
}

// TODO: Replace with your actual websocket endpoint
const WEBSOCKET_ENDPOINT = "ws://localhost:8080/ws/transcribe" // Replace this with your actual endpoint

export function RecordingScreen({ onNavigate, appState, updateAppState }: RecordingScreenProps) {
  const [recordingTime, setRecordingTime] = useState("00:00")
  const [startTime, setStartTime] = useState<number | null>(null)

  // Initialize websocket recording
  const {
    isConnected,
    isRecording: wsIsRecording,
    transcriptData,
    error: wsError,
    startRecording: wsStartRecording,
    stopRecording: wsStopRecording,
    pauseRecording: wsPauseRecording,
    resumeRecording: wsResumeRecording,
  } = useWebSocketRecording({
    websocketUrl: WEBSOCKET_ENDPOINT,
    onTranscriptionUpdate: (data) => {
      // Update app state with new transcription data
      updateAppState({ transcriptData: data });
    },
    onError: (error) => {
      console.error('WebSocket recording error:', error);
    },
    onConnectionChange: (connected) => {
      console.log('WebSocket connection status:', connected);
    },
  });

  useEffect(() => {
    // Auto-start recording when component mounts
    if (!appState.isRecording && !wsIsRecording) {
      handleStartRecording()
    }
  }, [])

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null

    if (appState.isRecording && !appState.isPaused && startTime) {
      interval = setInterval(() => {
        const elapsed = Date.now() - startTime
        const minutes = Math.floor(elapsed / 60000)
        const seconds = Math.floor((elapsed % 60000) / 1000)
        setRecordingTime(`${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`)
      }, 1000)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [appState.isRecording, appState.isPaused, startTime])

  const handleStartRecording = async () => {
    try {
      const now = Date.now()
      setStartTime(now)

      // Start websocket recording
      await wsStartRecording()

      updateAppState({
        isRecording: true,
        isPaused: false,
        recordingStartTime: now,
      })
    } catch (error) {
      console.error('Failed to start recording:', error)
    }
  }

  const handlePauseRecording = () => {
    wsPauseRecording()
    updateAppState({ isPaused: true })
  }

  const handleResumeRecording = () => {
    wsResumeRecording()
    updateAppState({ isPaused: false })
  }

  const handleStopRecording = () => {
    // Stop websocket recording
    wsStopRecording()

    updateAppState({
      isRecording: false,
      isPaused: false,
    })

    // Navigate directly to transcription screen with real data
    onNavigate("transcription-screen")
  }

  // Use real transcription data or fallback to empty state
  const currentTranscriptData = transcriptData || appState.transcriptData

  return (
    <div className="container mx-auto px-4 py-8 flex flex-col h-screen">
      <div className="flex items-center justify-between mb-8 pb-4 border-b flex-shrink-0">
        <h2 className="text-2xl font-semibold">Recording Interview</h2>
        <div className="flex items-center gap-4">
          {/* Connection Status */}
          <div className="flex items-center gap-2">
            {isConnected ? (
              <Wifi className="w-4 h-4 text-green-500" />
            ) : (
              <WifiOff className="w-4 h-4 text-red-500" />
            )}
            <span className="text-sm text-muted-foreground">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>

          {/* Recording Status */}
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            <Badge variant="secondary">{recordingTime}</Badge>
          </div>
        </div>
      </div>

      {/* Error Alert */}
      {wsError && (
        <Alert className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {wsError}
          </AlertDescription>
        </Alert>
      )}

      <Card className="mb-8 flex-shrink-0">
        <CardContent className="p-4">
          <div className="space-y-2">
            <p>
              <strong>Case:</strong> {appState.currentCase?.id}
            </p>
            <p>
              <strong>Witness:</strong> {appState.currentWitness?.name}
            </p>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-8 flex-1">
        <CardContent className="p-8">
          <div className="max-h-96 overflow-y-auto p-4">
            {currentTranscriptData?.segments && currentTranscriptData.segments.length > 0 ? (
              currentTranscriptData.segments.map((segment, index) => {
                const speaker = currentTranscriptData.speakers.find((s) => s.id === segment.speaker)
                return (
                  <div key={index} className="flex gap-4 mb-4">
                    <div
                      className="min-w-[120px] text-right pr-3 border-r-2 text-sm font-medium"
                      style={{ borderRightColor: speaker?.color }}
                    >
                      {speaker?.name}
                      <div className="text-xs text-muted-foreground font-normal">{segment.timestamp}</div>
                    </div>
                    <div className="flex-1 text-sm leading-relaxed">{segment.text}</div>
                  </div>
                )
              })
            ) : (
              <div className="text-center text-muted-foreground py-8">
                <p className="mb-2">Waiting for transcription...</p>
                <p className="text-sm">
                  {isConnected ? 'Connected to transcription service' : 'Connecting to transcription service...'}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="flex flex-col gap-4 max-w-sm mx-auto flex-shrink-0">
        {!appState.isPaused ? (
          <Button variant="secondary" size="lg" onClick={handlePauseRecording} className="flex items-center gap-2">
            <Pause className="w-5 h-5" />
            Pause
          </Button>
        ) : (
          <Button variant="secondary" size="lg" onClick={handleResumeRecording} className="flex items-center gap-2">
            <Play className="w-5 h-5" />
            Resume
          </Button>
        )}

        <Button size="lg" onClick={handleStopRecording} className="flex items-center gap-2">
          <Square className="w-5 h-5" />
          Stop Interview
        </Button>
      </div>
    </div>
  )
}
